# Design Document

## Overview

This design document outlines the improvements to the LLM price editing modal, transforming it from a tab-based interface to a unified, compact layout that allows simultaneous editing of all price fields. The design focuses on maximizing information density while maintaining excellent usability and visual hierarchy.

## Architecture

### Current Architecture
- Tab-based navigation separating price categories (Text, Audio, Image, Reasoning)
- Large modal with significant whitespace
- Standard font sizes and spacing
- Individual cards for each price category

### New Architecture
- Single-page layout with all price fields visible
- Compact grid-based organization
- Reduced font sizes and spacing
- Visual grouping through subtle styling rather than separate tabs

## Components and Interfaces

### 1. Modal Container
- **Size**: Maintain `sm:max-w-[80%]` but optimize internal spacing
- **Height**: Keep `max-h-[90vh] overflow-y-auto` for scrollability
- **Padding**: Reduce internal padding from default to compact spacing

### 2. Header Section
- **Title**: Use `text-lg` instead of default `text-xl`
- **Description**: Use `text-xs` instead of `text-sm`
- **Spacing**: Reduce header padding to `pb-3` instead of default

### 3. Basic Information Section
- **Layout**: Maintain 3-column grid but with reduced spacing
- **Font Sizes**: 
  - Labels: `text-xs font-medium`
  - Values: `text-sm`
  - Descriptions: `text-xs text-muted-foreground`
- **Spacing**: Use `gap-3` instead of `gap-4`

### 4. Price Fields Layout

#### Grid Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Basic Info (3-column grid)                                  │
├─────────────────────────────────────────────────────────────┤
│ Text Prices          │ Audio Prices        │ Image Prices   │
│ ┌─────────────────┐  │ ┌─────────────────┐ │ ┌────────────┐ │
│ │ Input Price     │  │ │ Input Price     │ │ │ Input      │ │
│ │ Output Price    │  │ │ Output Price    │ │ │ Output     │ │
│ │ Cache Price     │  │ │ Cache Price     │ │ │ Cache      │ │
│ │ 5m Cache Write  │  │ └─────────────────┘ │ └────────────┘ │
│ │ 1h Cache Write  │  │                     │                │
│ └─────────────────┘  │                     │                │
├─────────────────────────────────────────────────────────────┤
│ Reasoning Prices (single column, centered)                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Reasoning Completion Price                              │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Visual Grouping
- **Background**: Use subtle background colors to distinguish sections
  - Text: `bg-blue-50/30`
  - Audio: `bg-green-50/30`
  - Image: `bg-purple-50/30`
  - Reasoning: `bg-orange-50/30`
- **Borders**: Use `border border-gray-100` for subtle separation
- **Rounded Corners**: Apply `rounded-md` for modern appearance

### 5. Form Elements

#### Input Fields
- **Size**: Use compact input sizing
- **Font**: `text-sm` for input text
- **Placeholder**: Maintain `0.000000` format
- **Spacing**: Reduce margin between label and input

#### Labels and Descriptions
- **Primary Labels**: `text-xs font-medium text-gray-700`
- **Descriptions**: `text-xs text-gray-500`
- **Category Headers**: `text-sm font-semibold text-gray-800`

#### Icons and Visual Indicators
- Add small icons to distinguish price categories:
  - Text: `Type` icon
  - Audio: `Volume2` icon
  - Image: `Image` icon
  - Reasoning: `Brain` icon

## Data Models

### Form Schema Updates
No changes to the existing form schema - maintain all current validation rules and field types.

### Price Field Organization
```typescript
interface PriceFieldGroup {
  category: 'text' | 'audio' | 'image' | 'reasoning';
  icon: LucideIcon;
  bgColor: string;
  fields: PriceField[];
}

interface PriceField {
  name: string;
  label: string;
  description: string;
  placeholder: string;
}
```

## Error Handling

### Validation
- Maintain all existing validation rules
- Display validation errors inline with compact styling
- Use `text-xs text-red-600` for error messages

### Loading States
- Keep existing skeleton loading pattern
- Adjust skeleton heights to match compact layout
- Use smaller skeleton elements for reduced spacing

## Testing Strategy

### Visual Regression Testing
1. **Layout Consistency**: Verify grid alignment across different screen sizes
2. **Font Scaling**: Ensure readability at smaller font sizes
3. **Color Contrast**: Validate accessibility with new background colors
4. **Responsive Behavior**: Test layout adaptation on mobile devices

### Functional Testing
1. **Form Validation**: Ensure all existing validation continues to work
2. **Data Submission**: Verify API integration remains intact
3. **Error Handling**: Test error display with compact layout
4. **Accessibility**: Validate keyboard navigation and screen reader compatibility

### User Experience Testing
1. **Information Density**: Verify users can efficiently scan all price fields
2. **Edit Workflow**: Test the improved editing experience
3. **Visual Hierarchy**: Ensure users can distinguish between price categories
4. **Mobile Usability**: Validate touch interaction on smaller screens

## Implementation Details

### CSS Classes Structure
```css
/* Modal container */
.price-modal-content {
  @apply sm:max-w-[80%] max-h-[90vh] overflow-y-auto;
}

/* Compact spacing */
.compact-header {
  @apply pb-3;
}

.compact-section {
  @apply space-y-3;
}

/* Price category groups */
.price-group {
  @apply p-3 rounded-md border border-gray-100;
}

.price-group-text {
  @apply bg-blue-50/30;
}

.price-group-audio {
  @apply bg-green-50/30;
}

.price-group-image {
  @apply bg-purple-50/30;
}

.price-group-reasoning {
  @apply bg-orange-50/30;
}

/* Typography */
.compact-title {
  @apply text-lg font-semibold;
}

.compact-description {
  @apply text-xs text-muted-foreground;
}

.compact-label {
  @apply text-xs font-medium text-gray-700;
}

.compact-field-description {
  @apply text-xs text-gray-500;
}
```

### Responsive Breakpoints
- **Desktop (lg+)**: 3-column layout for main price groups
- **Tablet (md)**: 2-column layout with reasoning section below
- **Mobile (sm)**: Single column stack layout
- **Small Mobile**: Maintain single column with adjusted padding

### Accessibility Considerations
- Maintain proper heading hierarchy
- Ensure sufficient color contrast ratios
- Preserve keyboard navigation order
- Add aria-labels for price category sections
- Maintain focus management for form fields