{"name": "paotubao-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "uat": "vite --mode production"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@lobehub/icons": "^2.2.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.10", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-table": "^8.21.3", "ahooks": "^3.8.5", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clipboard-polyfill": "^4.1.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.7.4", "input-otp": "^1.4.2", "jotai": "^2.12.3", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "pushmodal": "^1.0.4", "react": "^19.0.0", "react-aria-components": "^1.8.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.5.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.4", "ts-pattern": "^5.7.1", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@stagewise/toolbar-react": "^0.1.2", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}