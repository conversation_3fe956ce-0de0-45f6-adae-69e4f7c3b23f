{"permissions": {"allow": ["<PERSON><PERSON>(./gradlew:*)", "Bash(find:*)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(bun run lint:*)", "Bash(bun run build:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bun run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)"], "deny": []}}