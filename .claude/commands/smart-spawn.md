# 智能Agent启动器

根据项目计划智能启动指定的Agent，传递必要的上下文信息。

## 使用方法
```bash
claude smart-spawn <agent-type> "<specific-task-description>"
```

## 执行逻辑

### 1. 验证启动条件
```bash
#!/bin/bash
set -euo pipefail

AGENT_TYPE="$1"
TASK_DESCRIPTION="$2"

# 参数检查
if [[ $# -lt 2 ]]; then
    echo "❌ 用法: claude smart-spawn <agent-type> \"<task-description>\""
    echo "可用Agent: database-agent, backend-agent, frontend-agent, review-code"
    exit 1
fi

# 检查项目状态文件
if [ ! -f "docs/status/project-status.yaml" ]; then
    echo "❌ 错误: 请先运行 analyze-and-plan 命令进行项目规划"
    echo "执行: claude analyze-and-plan \"您的项目需求\""
    exit 1
fi

# 检查Agent类型是否有效
VALID_AGENTS=("database-agent" "backend-agent" "frontend-agent" "review-code")
if [[ ! " ${VALID_AGENTS[*]} " =~ " ${AGENT_TYPE} " ]]; then
    echo "❌ 错误: Agent类型 '$AGENT_TYPE' 不存在"
    echo "可用的Agent类型: ${VALID_AGENTS[*]}"
    exit 1
fi

# 检查Agent命令文件
AGENT_COMMAND=".claude/commands/agents/$AGENT_TYPE.md"
if [ ! -f "$AGENT_COMMAND" ]; then
    echo "❌ 错误: Agent类型 '$AGENT_TYPE' 不存在"
    echo "可用的Agent类型: database-agent, backend-agent, frontend-agent, review-code"
    exit 1
fi
```

### 2. 检查Agent依赖
```bash
# 获取Agent依赖列表
echo "🔍 检查 $AGENT_TYPE 的依赖关系..."
DEPENDENCIES=$(yq eval ".agents.$AGENT_TYPE.dependencies[]?" docs/status/project-status.yaml 2>/dev/null || echo "")

if [[ -z "$DEPENDENCIES" ]]; then
    echo "✅ 无依赖，可以直接启动"
else
    # 检查每个依赖的状态
    for dep in $DEPENDENCIES; do
        if [[ -n "$dep" ]]; then
            DEP_STATUS=$(yq eval ".agents.$dep.status" docs/status/project-status.yaml 2>/dev/null || echo "unknown")
            case "$DEP_STATUS" in
                "completed")
                    echo "  ✅ $dep (已完成)"
                    ;;
                "running")
                    echo "  🔄 $dep (运行中)"
                    echo "⚠️  依赖检查失败: $dep 正在运行中"
                    echo "建议等待完成或使用 --force 强制启动"
                    exit 1
                    ;;
                "planned"|"unknown")
                    echo "  ⏳ $dep (未开始)"
                    echo "⚠️  依赖检查失败: $dep 尚未完成 (状态: $DEP_STATUS)"
                    echo "建议操作:"
                    echo "  1. 先启动依赖: claude smart-spawn $dep \"完成${dep}任务\""
                    echo "  2. 标记为可选: claude mark-dependency-optional $AGENT_TYPE $dep"
                    echo "  3. 查看依赖关系: claude show-dependencies $AGENT_TYPE"
                    exit 1
                    ;;
                "failed")
                    echo "  ❌ $dep (失败)"
                    echo "⚠️  依赖检查失败: $dep 执行失败"
                    echo "建议先修复失败的依赖或标记为可选"
                    exit 1
                    ;;
            esac
        fi
    done
    echo "✅ 所有依赖已满足"
fi
```

### 3. 准备Agent上下文
```bash
echo "📋 准备Agent上下文信息..."

# 收集相关的输入信息
PROJECT_CONTEXT=""
if [[ -f "docs/analysis/project-analysis.yaml" ]]; then
    PROJECT_CONTEXT=$(cat docs/analysis/project-analysis.yaml)
fi

PREVIOUS_OUTPUTS=""

# 收集前置Agent的输出
for dep in $DEPENDENCIES; do
    if [[ -n "$dep" && -f "docs/agents/$dep-outputs.yaml" ]]; then
        PREVIOUS_OUTPUTS="$PREVIOUS_OUTPUTS\n$(cat docs/agents/$dep-outputs.yaml)"
    fi
done

# 生成Agent专用的上下文文件
mkdir -p docs/agents
cat > "docs/agents/$AGENT_TYPE-context.yaml" << EOF
agent_type: "$AGENT_TYPE"
task_description: "$TASK_DESCRIPTION"
project_context: |
$PROJECT_CONTEXT
previous_outputs: |
$PREVIOUS_OUTPUTS
started_at: "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
status: "running"
EOF

echo "✅ 上下文文件已生成: docs/agents/$AGENT_TYPE-context.yaml"
```

### 4. 启动指定Agent
```bash
echo "🚀 启动 $AGENT_TYPE Agent..."
echo "📝 任务描述: $TASK_DESCRIPTION"
echo "📁 工作目录: docs/agents/"
echo "⏰ 开始时间: $(date)"

# 更新Agent状态为运行中
yq eval ".agents.$AGENT_TYPE.status = \"running\"" -i docs/status/project-status.yaml
yq eval ".agents.$AGENT_TYPE.last_updated = \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"" -i docs/status/project-status.yaml

# 记录操作日志
echo "$(date): 启动 $AGENT_TYPE Agent - $TASK_DESCRIPTION" >> docs/agents/operations.log

# 执行Agent命令
claude --command="$AGENT_COMMAND" --context="docs/agents/$AGENT_TYPE-context.yaml"

echo "✅ Agent启动成功！"
echo "📊 使用 'claude status' 查看进展"
```

## 智能特性

### 自动依赖解析
如果检测到依赖未满足，提供智能建议：
```bash
suggest_dependency_resolution() {
    local agent_type="$1"
    local failed_dep="$2"
    
    echo "🤔 检测到依赖问题，建议解决方案："
    echo "1. 先启动依赖的Agent: claude smart-spawn $failed_dep \"完成${failed_dep}任务\""
    echo "2. 如果不需要该依赖: claude mark-dependency-optional $agent_type $failed_dep"
    echo "3. 查看详细依赖关系: claude show-dependencies $agent_type"
    echo "4. 强制启动(跳过依赖): claude smart-spawn $agent_type \"$TASK_DESCRIPTION\" --force"
}
```

### 上下文智能传递
每个Agent只接收必要的信息，避免信息过载：
```yaml
context_filtering:
  database-agent:
    needs: ["project_requirements", "technical_constraints"]
    skip: ["ui_designs", "frontend_frameworks"]
  
  backend-agent:
    needs: ["database_schema", "api_requirements", "business_logic"]
    skip: ["ui_components", "styling_guides"]
  
  frontend-agent:
    needs: ["api_documentation", "ui_requirements", "user_flows"]
    skip: ["database_internals", "server_configs"]
  
  review-code:
    needs: ["all_outputs", "code_files", "project_requirements"]
    skip: []
```

### 错误处理和恢复
```bash
# 错误处理函数
handle_error() {
    local agent_type="$1"
    local error_msg="$2"
    
    echo "❌ Agent执行出错: $error_msg"
    
    # 更新状态为失败
    yq eval ".agents.$agent_type.status = \"failed\"" -i docs/status/project-status.yaml
    yq eval ".agents.$agent_type.last_updated = \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"" -i docs/status/project-status.yaml
    
    # 记录错误日志
    echo "$(date): Agent $agent_type 执行失败 - $error_msg" >> docs/agents/error.log
    
    echo "🔧 故障排除建议:"
    echo "1. 查看错误日志: cat docs/agents/error.log"
    echo "2. 重置Agent状态: claude reset-agent $agent_type"
    echo "3. 重新启动: claude smart-spawn $agent_type \"$TASK_DESCRIPTION\""
}

# 设置错误陷阱
trap 'handle_error "$AGENT_TYPE" "执行过程中发生未知错误"' ERR
```

## 高级选项

### 强制启动模式
```bash
# 支持 --force 参数跳过依赖检查
if [[ "${3:-}" == "--force" ]]; then
    echo "⚠️  强制启动模式：跳过依赖检查"
    SKIP_DEPENDENCY_CHECK=true
fi
```

### 并行执行支持
```bash
# 检查是否可以并行执行
check_parallel_execution() {
    local agent_type="$1"
    
    # 获取当前运行的Agent
    local running_agents
    running_agents=$(yq eval '.agents | to_entries | .[] | select(.value.status == "running") | .key' docs/status/project-status.yaml)
    
    if [[ -n "$running_agents" ]]; then
        echo "🔄 检测到正在运行的Agent: $running_agents"
        echo "是否并行执行? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "❌ 取消启动，等待当前Agent完成"
            exit 1
        fi
    fi
}
```
