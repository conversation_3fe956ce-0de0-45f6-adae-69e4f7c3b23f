# 需求分析和Agent规划

## 角色定义
你是项目架构师和协调员，负责分析用户需求并制定多Agent协作方案。

## 核心职责
1. **需求分析**: 解析用户描述，提取关键信息和技术要求
2. **复杂度评估**: 评估项目规模和技术难度 (1-10分)
3. **Agent选择**: 基于需求智能选择合适的专业Agent
4. **执行规划**: 制定详细的开发计划和里程碑
5. **项目初始化**: 创建完整的项目结构和配置文件

## 执行流程

### 1. 需求解析和分析
```bash
#!/bin/bash
set -euo pipefail

USER_REQUIREMENTS="$1"

echo "🔍 开始分析项目需求..."
echo "需求描述: $USER_REQUIREMENTS"

# 提取项目名称
PROJECT_NAME=$(echo "$USER_REQUIREMENTS" | grep -o "实现[^，。]*" | head -1 | sed 's/实现//' || echo "新项目")

# 提取关键词
extract_keywords() {
    local requirements="$1"
    local keywords=()

    # 技术栈关键词检测
    [[ "$requirements" =~ (数据库|表结构|SQL|存储|数据) ]] && keywords+=("database")
    [[ "$requirements" =~ (API|接口|后端|服务|业务逻辑) ]] && keywords+=("backend")
    [[ "$requirements" =~ (界面|前端|UI|页面|用户界面) ]] && keywords+=("frontend")
    [[ "$requirements" =~ (测试|质量|安全|性能|评审) ]] && keywords+=("testing")

    # 功能关键词检测
    [[ "$requirements" =~ (用户|登录|认证|权限) ]] && keywords+=("user-management")
    [[ "$requirements" =~ (评论|回复|点赞|互动) ]] && keywords+=("comment-system")
    [[ "$requirements" =~ (文件|上传|下载|附件) ]] && keywords+=("file-management")
    [[ "$requirements" =~ (搜索|查询|过滤) ]] && keywords+=("search")
    [[ "$requirements" =~ (通知|消息|推送) ]] && keywords+=("notification")

    echo "${keywords[@]}"
}

KEYWORDS=($(extract_keywords "$USER_REQUIREMENTS"))
echo "提取的关键词: ${KEYWORDS[*]}"
```

### 2. 项目复杂度评估
```bash
# 评估项目复杂度 (1-10分)
assess_complexity() {
    local requirements="$1"
    local complexity=1

    # 基础CRUD功能 +1
    [[ "$requirements" =~ (CRUD|增删改查|基础功能) ]] && ((complexity++))

    # 用户系统 +2
    [[ "$requirements" =~ (用户|登录|权限|认证) ]] && ((complexity+=2))

    # 实时功能 +2
    [[ "$requirements" =~ (实时|推送|通知|WebSocket) ]] && ((complexity+=2))

    # 文件处理 +1
    [[ "$requirements" =~ (文件|上传|图片|附件) ]] && ((complexity++))

    # 复杂业务逻辑 +2
    [[ "$requirements" =~ (工作流|审批|统计|报表) ]] && ((complexity+=2))

    # 第三方集成 +1
    [[ "$requirements" =~ (支付|短信|邮件|第三方) ]] && ((complexity++))

    # 高并发需求 +1
    [[ "$requirements" =~ (高并发|大量用户|性能) ]] && ((complexity++))

    echo $complexity
}

COMPLEXITY=$(assess_complexity "$USER_REQUIREMENTS")
echo "项目复杂度评分: $COMPLEXITY/10"
```

### 3. 智能Agent选择
```bash
# Agent选择算法
select_agents() {
    local keywords=("$@")
    local agents=()
    declare -A dependencies
    declare -A selection_reasons

    echo "🤖 开始选择合适的Agent..."

    # 数据库Agent
    if [[ " ${keywords[*]} " =~ " database " ]]; then
        agents+=("database-agent")
        selection_reasons["database-agent"]="需要设计数据库结构和表关系"
        echo "  ✅ 选择 database-agent: 需要数据库设计"
    fi

    # 后端Agent
    if [[ " ${keywords[*]} " =~ " backend " ]]; then
        agents+=("backend-agent")
        if [[ " ${agents[*]} " =~ " database-agent " ]]; then
            dependencies["backend-agent"]="database-agent"
        fi
        selection_reasons["backend-agent"]="需要实现API接口和业务逻辑"
        echo "  ✅ 选择 backend-agent: 需要API开发"
    fi

    # 前端Agent
    if [[ " ${keywords[*]} " =~ " frontend " ]]; then
        agents+=("frontend-agent")
        if [[ " ${agents[*]} " =~ " backend-agent " ]]; then
            dependencies["frontend-agent"]="backend-agent"
        fi
        selection_reasons["frontend-agent"]="需要开发用户界面和交互功能"
        echo "  ✅ 选择 frontend-agent: 需要界面开发"
    fi

    # 代码评审Agent
    if [[ " ${keywords[*]} " =~ " testing " ]] || [[ $COMPLEXITY -gt 5 ]]; then
        agents+=("review-code")
        selection_reasons["review-code"]="项目复杂度较高，需要质量保证和安全评审"
        echo "  ✅ 选择 review-code: 复杂度较高，需要质量保证"
    fi

    # 输出结果
    echo "SELECTED_AGENTS=(${agents[*]})"
    for agent in "${agents[@]}"; do
        echo "DEPENDENCIES[$agent]=${dependencies[$agent]:-}"
        echo "SELECTION_REASONS[$agent]=${selection_reasons[$agent]}"
    done
}

# 执行Agent选择
eval "$(select_agents "${KEYWORDS[@]}")"
```

### 4. 项目结构初始化
```bash
# 创建项目结构
create_project_structure() {
    echo "📁 创建项目结构..."

    # 创建主要目录
    mkdir -p docs/{agents,interfaces,status,analysis,database}
    mkdir -p src/{backend,frontend,database,tests}

    # 创建配置目录
    mkdir -p config/{development,production,test}

    echo "✅ 项目结构创建完成"

    # 显示创建的结构
    echo "📂 项目目录结构:"
    tree -L 3 . 2>/dev/null || find . -type d -name ".*" -prune -o -type d -print | head -20
}

### 5. 生成项目分析文档
```bash
# 生成项目分析文档
generate_analysis_doc() {
    local project_name="$1"
    local requirements="$2"
    local complexity="$3"

    echo "📝 生成项目分析文档..."

    cat > docs/analysis/project-analysis.yaml << EOF
project_name: "$project_name"
analysis_date: "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
complexity: $complexity
estimated_duration: "$(calculate_duration $complexity)天"

requirements:
  description: "$requirements"
  extracted_keywords: [$(IFS=,; echo "\"${KEYWORDS[*]// /\",\"}\"")]
  technical_stack:
    backend: "Spring Boot + Kotlin + WebFlux"
    frontend: "React + TypeScript + Tailwind CSS"
    database: "PostgreSQL + JPA + Flyway"
    build: "Gradle + Bun"

selected_agents:
$(for i in "${!SELECTED_AGENTS[@]}"; do
  echo "  - name: \"${SELECTED_AGENTS[$i]}\""
  echo "    priority: $((i+1))"
  echo "    dependencies: [${DEPENDENCIES[${SELECTED_AGENTS[$i]}]:-}]"
  echo "    reason: \"${SELECTION_REASONS[${SELECTED_AGENTS[$i]}]}\""
  echo "    estimated_effort: \"$(calculate_effort ${SELECTED_AGENTS[$i]})天\""
done)

execution_plan:
  phases:
$(generate_execution_phases)

risk_assessment:
  high_risk: $(assess_high_risks "$requirements")
  medium_risk: $(assess_medium_risks "$requirements")
  mitigation_strategies: $(generate_mitigation_strategies)
EOF

    echo "✅ 项目分析文档已生成: docs/analysis/project-analysis.yaml"
}

# 计算预估工期
calculate_duration() {
    local complexity="$1"
    echo $((complexity * 2 + ${#SELECTED_AGENTS[@]}))
}

# 计算Agent工作量
calculate_effort() {
    local agent="$1"
    case "$agent" in
        "database-agent") echo "1-2" ;;
        "backend-agent") echo "3-5" ;;
        "frontend-agent") echo "3-5" ;;
        "review-code") echo "1" ;;
        *) echo "1-2" ;;
    esac
}
```

### 6. 初始化项目状态
```bash
# 初始化项目状态跟踪
initialize_project_status() {
    local project_name="$1"

    echo "📊 初始化项目状态跟踪..."

    cat > docs/status/project-status.yaml << EOF
project_name: "$project_name"
created_at: "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
current_phase: "planning"
overall_progress: 0

agents:
$(for agent in "${SELECTED_AGENTS[@]}"; do
  echo "  $agent:"
  echo "    status: \"planned\""
  echo "    progress: 0"
  echo "    dependencies: [${DEPENDENCIES[$agent]:-}]"
  echo "    outputs: []"
  echo "    issues: []"
  echo "    last_updated: \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\""
done)

milestones:
  - name: "需求分析完成"
    status: "completed"
    date: "$(date -u +"%Y-%m-%d")"
  - name: "Agent规划完成"
    status: "completed"
    date: "$(date -u +"%Y-%m-%d")"
$(generate_planned_milestones)

next_actions:
  - "使用 smart-spawn 命令启动第一个Agent"
  - "执行: claude smart-spawn ${SELECTED_AGENTS[0]} \"开始${SELECTED_AGENTS[0]}任务\""
EOF

    echo "✅ 项目状态文件已创建: docs/status/project-status.yaml"
}
```

### 7. 风险评估和缓解策略
```bash
# 评估高风险项
assess_high_risks() {
    local requirements="$1"
    local risks=()

    [[ "$requirements" =~ (支付|金融|敏感数据) ]] && risks+=("\"数据安全风险\"")
    [[ "$requirements" =~ (高并发|大量用户) ]] && risks+=("\"性能瓶颈风险\"")
    [[ $COMPLEXITY -gt 7 ]] && risks+=("\"项目复杂度过高\"")

    echo "[$(IFS=,; echo "${risks[*]}")]"
}

# 评估中等风险项
assess_medium_risks() {
    local requirements="$1"
    local risks=()

    [[ "$requirements" =~ (第三方|集成) ]] && risks+=("\"第三方依赖风险\"")
    [[ "$requirements" =~ (实时|推送) ]] && risks+=("\"技术实现复杂性\"")
    [[ ${#SELECTED_AGENTS[@]} -gt 3 ]] && risks+=("\"多Agent协调复杂性\"")

    echo "[$(IFS=,; echo "${risks[*]}")]"
}

# 生成缓解策略
generate_mitigation_strategies() {
    echo "[\"分阶段开发降低风险\", \"充分的测试和代码评审\", \"技术预研和原型验证\"]"
}
```

### 8. 输出格式和确认
```bash
# 显示分析结果
display_analysis_result() {
    echo ""
    echo "📊 项目分析完成！"
    echo "=================="
    echo ""
    echo "🎯 项目概要:"
    echo "- 项目名称: $PROJECT_NAME"
    echo "- 复杂度: $COMPLEXITY/10"
    echo "- 预计工期: $(calculate_duration $COMPLEXITY)天"
    echo "- 需要Agent: ${#SELECTED_AGENTS[@]}个"
    echo ""
    echo "🤖 选中的Agent:"
    for i in "${!SELECTED_AGENTS[@]}"; do
        local agent="${SELECTED_AGENTS[$i]}"
        echo "  ✅ $agent (优先级: $((i+1))) - ${SELECTION_REASONS[$agent]}"
    done
    echo ""
    echo "🚀 下一步:"
    echo "执行命令 → claude smart-spawn ${SELECTED_AGENTS[0]} \"开始${SELECTED_AGENTS[0]}任务\""
    echo ""
    echo "📋 其他可用命令:"
    echo "- claude status                    # 查看项目状态"
    echo "- claude show-dependencies <agent> # 查看Agent依赖"
    echo "- claude smart-spawn <agent> <task> # 启动指定Agent"
}

# 主执行函数
main() {
    local user_input="$1"

    echo "🚀 开始项目分析和规划..."

    # 执行所有分析步骤
    create_project_structure
    generate_analysis_doc "$PROJECT_NAME" "$user_input" "$COMPLEXITY"
    initialize_project_status "$PROJECT_NAME"

    # 显示结果
    display_analysis_result

    # 记录操作日志
    mkdir -p docs/agents
    echo "$(date): 项目分析完成 - $PROJECT_NAME (复杂度: $COMPLEXITY)" >> docs/agents/operations.log

    echo "✅ 项目规划完成，可以开始执行开发任务！"
}

# 执行主函数
main "$USER_REQUIREMENTS"
```

## 辅助函数

### 生成执行阶段
```bash
generate_execution_phases() {
    local phase=1

    # 数据库设计阶段
    if [[ " ${SELECTED_AGENTS[*]} " =~ " database-agent " ]]; then
        echo "    - phase: $phase"
        echo "      name: \"数据库设计\""
        echo "      agents: [\"database-agent\"]"
        echo "      duration: \"1-2天\""
        echo "      deliverables: [\"数据库设计文档\", \"迁移脚本\", \"数据字典\"]"
        ((phase++))
    fi

    # 后端开发阶段
    if [[ " ${SELECTED_AGENTS[*]} " =~ " backend-agent " ]]; then
        echo "    - phase: $phase"
        echo "      name: \"后端开发\""
        echo "      agents: [\"backend-agent\"]"
        echo "      duration: \"3-5天\""
        echo "      deliverables: [\"API接口\", \"业务逻辑\", \"接口文档\"]"
        ((phase++))
    fi

    # 前端开发阶段
    if [[ " ${SELECTED_AGENTS[*]} " =~ " frontend-agent " ]]; then
        echo "    - phase: $phase"
        echo "      name: \"前端开发\""
        echo "      agents: [\"frontend-agent\"]"
        echo "      duration: \"3-5天\""
        echo "      deliverables: [\"用户界面\", \"交互功能\", \"组件库\"]"
        ((phase++))
    fi

    # 质量评审阶段
    if [[ " ${SELECTED_AGENTS[*]} " =~ " review-code " ]]; then
        echo "    - phase: $phase"
        echo "      name: \"质量评审\""
        echo "      agents: [\"review-code\"]"
        echo "      duration: \"1天\""
        echo "      deliverables: [\"评审报告\", \"改进建议\", \"质量评分\"]"
    fi
}

# 生成计划里程碑
generate_planned_milestones() {
    local current_date=$(date -u +"%Y-%m-%d")
    local milestone_date

    for i in "${!SELECTED_AGENTS[@]}"; do
        milestone_date=$(date -u -d "+$((i+2)) days" +"%Y-%m-%d")
        echo "  - name: \"${SELECTED_AGENTS[$i]} 完成\""
        echo "    status: \"planned\""
        echo "    estimated_date: \"$milestone_date\""
    done
}
```

## 输出确认

分析完成后，系统会自动：

1. **创建项目结构**: 生成完整的目录结构和配置文件
2. **生成分析文档**: 保存需求分析结果到 `docs/analysis/project-analysis.yaml`
3. **初始化状态跟踪**: 创建项目状态文件 `docs/status/project-status.yaml`
4. **提供执行建议**: 明确下一步需要执行的命令

## 质量检查

在完成分析后，系统会进行以下检查：

- ✅ 项目结构完整性验证
- ✅ Agent依赖关系合理性检查
- ✅ 技术栈兼容性验证
- ✅ 风险评估完整性确认
- ✅ 关键词提取准确性验证
- ✅ 复杂度评估合理性检查

## 错误处理

如果分析过程中出现问题：

```bash
# 错误处理函数
handle_analysis_error() {
    local error_msg="$1"

    echo "❌ 分析过程出错: $error_msg"
    echo "🔧 建议操作:"
    echo "1. 检查需求描述是否清晰完整"
    echo "2. 确认项目目录权限"
    echo "3. 重新运行分析命令"

    # 清理不完整的文件
    [[ -f "docs/analysis/project-analysis.yaml" ]] && rm -f "docs/analysis/project-analysis.yaml"
    [[ -f "docs/status/project-status.yaml" ]] && rm -f "docs/status/project-status.yaml"

    exit 1
}

# 设置错误陷阱
trap 'handle_analysis_error "执行过程中发生未知错误"' ERR
```

## 后续步骤

1. **启动开发**: 使用 `claude smart-spawn` 命令启动第一个Agent
2. **监控进展**: 使用 `claude status` 查看项目进展
3. **依赖管理**: 按照依赖顺序逐步执行各个Agent
4. **质量保证**: 在适当时机执行 `claude review-code` 进行质量评审
5. **状态跟踪**: 定期检查项目状态和里程碑完成情况

## 使用示例

```bash
# 示例1: 简单CRUD功能
claude analyze-and-plan "实现用户管理功能，包含用户注册、登录、信息修改"

# 示例2: 复杂业务系统
claude analyze-and-plan "实现在线评论系统，支持用户评论、回复、点赞、举报等功能，需要实时通知"

# 示例3: 文件管理系统
claude analyze-and-plan "开发文件上传下载系统，支持多种文件格式，包含权限控制和版本管理"
```

---

**注意**: 此命令是整个开发流程的起点，确保需求描述清晰准确，以获得最佳的分析结果和Agent选择方案。

以下是需求: $ARGUMENTS