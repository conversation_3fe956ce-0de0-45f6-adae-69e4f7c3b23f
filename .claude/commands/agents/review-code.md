# 代码评审系统 - 精简版

基于智能代理的多层次自动化代码评审系统，确保代码质量和安全性。

## 核心组件

### 1. 智能评审触发器
- **自动检测**：识别已完成但未评审的代理模块
- **变更驱动**：代码更新后自动触发评审
- **状态跟踪**：基于YAML的评审状态管理

### 2. 四层评审体系

#### 架构评审代理
- **系统设计分析**：组件关系、耦合度评估
- **扩展性评估**：水平/垂直扩展准备度
- **技术栈验证**：框架和工具适配性
- **接口设计评审**：API一致性和标准符合度
- **依赖关系映射**：模块间连接分析

#### 安全评审代理
- **注入攻击防护**：SQL、NoSQL、命令注入检查
- **XSS防护**：输出编码、CSP策略、输入净化
- **认证安全**：密码策略、会话管理、双因素认证、JWT安全
- **授权控制**：访问矩阵、权限提升防护
- **数据保护**：加密传输、日志脱敏
- **合规检查**：OWASP Top 10、GDPR、行业标准

#### 性能评审代理
- **数据库优化**：慢查询分析、索引使用率、执行计划
- **API性能**：响应时间、吞吐量限制、瓶颈识别
- **前端指标**：加载时间、LCP、CLS、TTI测量
- **缓存策略**：命中率、失效策略、CDN效果
- **资源利用**：内存、CPU、网络分析

#### 代码质量评审代理
- **静态分析**：ESLint、Prettier、命名规范
- **复杂度指标**：圈复杂度、重构候选项
- **重复检测**：代码克隆识别和模式
- **测试覆盖**：语句、分支、函数、行覆盖率分析
- **依赖审计**：过期包、漏洞、许可证兼容性
- **技术债务**：量化改进领域

### 3. 综合报告系统

#### 单项报告
每个代理生成详细的markdown报告，包含：
- **量化指标**：0-10分评分体系
- **问题优先级**：高/中/低风险分类
- **具体修复步骤**：可执行的改进建议
- **性能基准**：可对比的测量数据

#### 统一仪表板
- **跨维度评分**：架构、安全、性能、质量综合评估
- **风险评估矩阵**：关键/重要/次要问题分类
- **改进路线图**：分阶段行动计划（1-2周、2-4周、1-2月）
- **质量门禁**：自动化通过/失败标准
- **合规跟踪**：标准遵循度监控

### 4. 执行模式
```bash
execute_review [范围]
# 选项：all(全部), security(安全), performance(性能), quality(质量), architecture(架构)
```

### 5. 关键特性
- **自动触发**：基于变更的评审启动
- **增量评审**：仅评审修改的组件
- **历史跟踪**：评审时间线和趋势分析
- **可执行洞察**：优先级修复建议
- **状态集成**：基于YAML的项目状态管理

该系统提供架构、安全、性能、质量四个维度的全面代码评估，自动生成报告并提供可执行的改进建议。

## 评审流程示例

### 评审触发条件
1. **代码变更**：检测到Agent状态为"completed"但未评审
2. **定期评审**：按计划执行的全面评审
3. **手动触发**：开发者主动发起的专项评审

### 评分体系
- **架构设计**：模块化程度、接口清晰度、扩展性
- **安全防护**：漏洞数量、防护覆盖率、合规性
- **性能表现**：响应时间、资源消耗、优化空间
- **代码质量**：可读性、测试覆盖率、技术债务

### 输出成果
- **问题清单**：按优先级排序的待修复问题
- **改进建议**：具体的技术解决方案
- **质量趋势**：项目质量变化轨迹
- **合规报告**：标准遵循情况总结