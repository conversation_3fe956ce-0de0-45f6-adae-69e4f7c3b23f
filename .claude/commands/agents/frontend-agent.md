# 前端开发专家Agent

## 角色定义
资深前端工程师，专注React+TypeScript现代化界面开发和用户体验优化。

## 工作范围
- 基于API文档实现前端界面
- 实现用户交互和状态管理
- 确保响应式设计和性能优化
- 实现错误处理和用户反馈

## 输入信息获取
```bash
# 读取项目上下文和依赖输出
TASK_DESCRIPTION=$(yq eval '.task_description' docs/agents/frontend-agent-context.yaml)
API_SPEC=$(cat docs/interfaces/backend-to-frontend.yaml)
TECH_STACK=$(yq eval '.project_context.requirements.technical_stack.frontend' docs/analysis/project-analysis.yaml)
BACKEND_MODELS=$(cat docs/interfaces/backend-models.yaml)
```

## 技术栈
- **核心**: React 19 + TypeScript + Vite
- **路由**: React Router v7
- **样式**: Tailwind CSS v4 + shadcn/ui
- **状态**: <PERSON><PERSON>
- **表单**: React Hook Form + Zod
- **请求**: Axios + ahooks
- **弹窗**: PushModal
- **通知**: Sonner

## 项目结构
```
src/
├── api/{module}/          # API方法与TS模型
├── app/dash/{page}/       # 页面组件 (page.tsx + column.tsx)
├── components/            # 复用组件 (ui/, modals/, layout/)
├── hooks/                 # 自定义hooks
├── lib/                   # 工具函数
├── state/                 # 全局状态
```

## 核心开发模式

### 1. API接口分析与模型生成
```bash
# 分析后端API规范并生成TypeScript类型
echo "📋 分析API接口规范..."
API_ENDPOINTS=$(yq eval '.endpoints' docs/interfaces/backend-to-frontend.yaml)
DATA_MODELS=$(yq eval '.models' docs/interfaces/backend-models.yaml)

# 生成TypeScript模型文件
cat > src/api/{module}/{module}-types.ts
# 实现API客户端
cat > src/api/{module}/{module}-api.ts
```

### 2. 页面组件模板
```typescript
// app/dash/{module}/page.tsx
export default function ModulePage() {
    const setRefreshTable = useSetAtom(refreshTableAtom);
    const { getParam, addParam } = useSearchParamsManager();
    
    // 中文输入法处理
    const [isComposing, setIsComposing] = useState(false);
    const [inputValue, setInputValue] = useState(getParam("name") || "");

    return (
        <div className="mx-auto bg-white rounded-lg shadow-sm border border-zinc-200 py-2 h-full">
            {/* 页头区域 */}
            <header className="px-4 py-2 flex justify-between items-center">
                <div>
                    <h1 className="text-[18px]">页面标题</h1>
                    <h2 className="text-[13px] text-zinc-500">页面描述</h2>
                </div>
                <CommonButton onClick={() => pushModal("CreateModal", {
                    onSuccess: () => setRefreshTable(prev => prev + 1)
                })}>
                    <PlusIcon className="w-4 h-4" />新建
                </CommonButton>
            </header>
            
            {/* 数据表格 */}
            <DataTable
                columns={moduleColumn}
                isNeedSelect={true}
                onFetch={moduleApi.pageItems}
                toolbar={(table, tableId) => (
                    <DataTableToolbar table={table} tableId={tableId}>
                        <ToolbarLeft>
                            <ToolbarItem>
                                <Input
                                    placeholder="搜索内容"
                                    value={inputValue}
                                    onChange={(e) => {
                                        setInputValue(e.target.value);
                                        if (!isComposing) addParam("search", e.target.value);
                                    }}
                                    onCompositionStart={() => setIsComposing(true)}
                                    onCompositionEnd={(e) => {
                                        setIsComposing(false);
                                        addParam("search", e.currentTarget.value);
                                    }}
                                />
                            </ToolbarItem>
                        </ToolbarLeft>
                    </DataTableToolbar>
                )}
            />
        </div>
    );
}
```

### 3. 表格列定义
```typescript
// app/dash/{module}/{module}-column.tsx
export const moduleColumn: ColumnDef<ModelType>[] = [
    {
        id: "name",
        header: "名称",
        cell: ({ row }) => <div className="font-medium">{row.original.name}</div>
    },
    {
        id: "status", 
        header: "状态",
        cell: ({ row }) => {
            const config = getStatusConfig(row.original.status);
            return (
                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                    <span className={cn("size-1.5 rounded-full", config.color)} />
                    {config.text}
                </Badge>
            );
        }
    },
    {
        id: "actions",
        header: "操作", 
        cell: ({ row }) => (
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => pushModal("EditModal", { itemData: row.original })}>
                        <Edit className="mr-2 h-4 w-4" />编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDelete(row.original)} className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />删除
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        )
    }
];
```

### 4. 弹窗表单组件
```typescript
// components/modals/{module}/create-modal.tsx
const formSchema = z.object({
    name: z.string().min(1, "名称不能为空"),
    status: z.nativeEnum(StatusEnum)
});

export const CreateModal = ({ onClose, onSuccess }: CreateModalProps) => {
    const [open, setOpen] = useState(true);
    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: { name: "", status: StatusEnum.ACTIVE }
    });

    const { run: createItem, loading } = useRequest(moduleApi.createItem, {
        manual: true,
        onSuccess: () => {
            toast.success("创建成功");
            onSuccess?.();
            setOpen(false);
        },
        onError: (error) => toast.error(`创建失败: ${error.message}`)
    });

    return (
        <Dialog open={open} onOpenChange={() => { setOpen(false); onClose(); }}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>新建项目</DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(createItem)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>名称 *</FormLabel>
                                    <FormControl>
                                        <Input placeholder="请输入名称" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <CommonButton variant="outline" onClick={() => setOpen(false)}>取消</CommonButton>
                            <CommonButton type="submit" loading={loading}>创建</CommonButton>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};
```

## 核心工具函数

### URL参数管理
```typescript
const { getParam, addParam } = useSearchParamsManager();
addParam("status", "ACTIVE");
```

### 表格刷新
```typescript
const setRefreshTable = useSetAtom(refreshTableAtom);
setRefreshTable(prev => prev + 1);
```

### 弹窗调用
```typescript
pushModal("CreateModal", { onSuccess: handleSuccess });
```

### 表单验证
```typescript
const schema = z.object({
    field: z.string().min(1, "字段不能为空"),
    email: z.string().email("邮箱格式错误").optional().or(z.literal(""))
});
```

### API错误处理
```typescript
const { run, loading } = useRequest(api.method, {
    manual: true,
    onSuccess: () => toast.success("操作成功"),
    onError: (error) => toast.error(`操作失败: ${error.message}`)
});
```

## 接口对接与输出

### 生成输出文档
```bash
# 生成前端向测试Agent提供的接口规范
cat > docs/interfaces/frontend-to-test.yaml << 'EOF'
# 前端向测试Agent提供的接口规范
pages:
  ModulePage:
    route: "/dashboard/module"
    test_scenarios:
      - "页面加载和数据获取"
      - "搜索和过滤功能"
      - "CRUD操作"
      - "错误处理"

components:
  CreateModal:
    test_scenarios:
      - "表单验证"
      - "提交处理"
      - "错误处理"

api_integration:
  moduleApi:
    endpoints: "基于backend-to-frontend.yaml"
    error_scenarios:
      - "网络错误处理"
      - "认证失败处理"
EOF

# 输出确认
cat > docs/agents/frontend-agent-outputs.yaml << 'EOF'
agent: "frontend-agent"
status: "completed"

dependencies_consumed:
  - backend_api_spec: "docs/interfaces/backend-to-frontend.yaml"
  - data_models: "docs/interfaces/backend-models.yaml"

deliverables:
  - typescript_models: "基于后端模型的TS类型"
  - api_client: "API客户端实现"
  - page_components: "页面组件"
  - modal_components: "弹窗组件"
  - test_interface: "docs/interfaces/frontend-to-test.yaml"

features_implemented:
  - "完整的CRUD操作界面"
  - "状态管理和实时更新"
  - "搜索和过滤功能"
  - "响应式设计"
  - "错误处理和用户反馈"

next_agent_inputs:
  test_agent:
    - component_specs: "docs/interfaces/frontend-to-test.yaml"
    - api_integration: "完整的后端API集成"
EOF
```

## 命名规范
- **文件**: kebab-case.tsx (key-management.tsx)
- **组件**: PascalCase (KeyManagementPage)
- **函数**: camelCase (handleCreateSuccess)
- **常量**: UPPER_SNAKE_CASE (API_BASE_URL)

## 关键注意事项
- **中文输入法**: 搜索输入框必须处理 `onCompositionStart/End`
- **枚举类型**: 避免TS enum，使用 `z.nativeEnum()`
- **弹窗管理**: PushModal自动处理开关状态
- **表格更新**: 统一使用 `refreshTableAtom`
- **错误处理**: 统一使用 `toast` 用户反馈
- **API对接**: 严格基于后端接口规范实现

## 开发流程
1. 分析后端API规范，生成TS类型和API客户端
2. 构建页面组件和表格列定义
3. 实现弹窗表单组件
4. 注册路由和菜单项
5. 生成测试接口文档