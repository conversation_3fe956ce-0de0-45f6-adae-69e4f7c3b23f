# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack key management system for AI services (Claude Code, Google Vertex AI, OpenAI) with automatic token refresh capabilities. The project consists of:
- **Backend**: Spring Boot 3.5.3 + Kotlin 1.9.25 (WebFlux/Reactive)
- **Frontend**: React 19 + TypeScript + Vite (located in `key-management-front/`)

## Essential Development Commands

### Backend Commands
```bash
# Run the backend application (port 8080)
./gradlew bootRun

# Build the application
./gradlew build

# Run tests
./gradlew test

# Clean and rebuild
./gradlew clean build
```

### Frontend Commands
```bash
# Navigate to frontend directory first
cd key-management-front

# Install dependencies
bun install  # or npm install

# Run development server (port 5173)
bun run dev  # or npm run dev

# Build for production
bun run build --mode=production

# Lint check (MUST run before committing)
bun run lint  # or npm run lint
```

### Database Setup
- PostgreSQL required on localhost:5432
- Database name: `key_management`
- Default credentials: username=postgres, password=root
- Migrations auto-applied via Flyway in `src/main/resources/db/migration/`

## Architecture Overview

### Backend Architecture (DDD-style)
```
io.cliveyou.claudecodeproxybackend/
├── keymanagement/
│   ├── application/      # Services & business logic
│   ├── domain/          # Domain models & exceptions
│   ├── facade/          # Controllers & DTOs
│   └── infrastructure/  # Repositories & external services
└── common/              # Shared utilities & config
```

Key patterns:
- Controllers return `Mono<PlatformResult<T>>` for reactive responses
- Services use Kotlin coroutines (`suspend fun`)
- Unified token management via `CommonCodeKey` entity
- Global exception handling via `GlobalWebFluxExceptionHandler`

### Frontend Architecture
```
key-management-front/src/
├── api/         # API clients with TypeScript models
├── app/dash/    # Dashboard pages (table-heavy CRUD)
├── components/  # Reusable UI components
├── hooks/       # Custom React hooks
├── state/       # Jotai atoms for global state
└── router/      # React Router v7 configuration
```

Key patterns:
- Table/CRUD pages follow template in `docs/frontend-development-guide.md`
- Forms use React Hook Form + Zod validation
- Modals managed via PushModal registration
- State updates via `refreshTableAtom` for table refreshes
- API responses wrapped in `PlatformResult<T>` type

## API Endpoints Structure

All APIs under `/api/private/` prefix:
- `POST /common-keys` - Create key
- `GET /common-keys/{id}` - Get key
- `PUT /common-keys/{id}` - Update key
- `DELETE /common-keys/{id}` - Delete key
- `POST /common-keys/page` - Paginated listing
- Special endpoints for each AI service type

## Critical Development Notes

1. **Frontend File Naming**: Use kebab-case for files, PascalCase for components
2. **TypeScript**: Don't use enums as types (erasableSyntaxOnly rule enforced)
3. **Chinese IME**: Search inputs must handle Chinese input method properly
4. **Database Changes**: Create Flyway migrations in `src/main/resources/db/migration/`
5. **Token Refresh**: Automatic refresh handled by scheduled tasks in backend
6. **CORS**: Enabled for all origins in development

## Testing Approach

- Backend: JUnit 5 + Spring Boot Test + Reactor Test
- Frontend: No test framework configured (would need to add if required)
- Run backend tests with `./gradlew test`
- Always run lint before committing frontend code

## Common Tasks

### Adding a New AI Service Type
1. Add enum value to `ApiServiceType`
2. Create domain model extending `CommonCodeKey`
3. Implement service in `application/` layer
4. Add controller endpoints in `facade/`
5. Update frontend API models and pages

### Creating a New CRUD Page
1. Follow template in `docs/frontend-development-guide.md`
2. Create API client in `src/api/`
3. Create page component in `src/app/dash/`
4. Register route in `src/router/`
5. Add modal forms using PushModal pattern