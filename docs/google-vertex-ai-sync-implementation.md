# Google Vertex AI 密钥同步功能实现

## 功能概述

实现了一个完整的Google Vertex AI密钥同步系统，能够定时从GCP MySQL数据库的channels表同步Google Vertex AI密钥数据到本地系统。

## 实现内容

### 1. 数据库配置
- **MySQL数据源配置**: `MySqlDataSourceConfig.kt`
  - 配置第二个数据源连接到GCP MySQL数据库
  - 支持多数据源JPA配置
  - 独立的事务管理器

### 2. 实体类
- **Channel实体**: `Channel.kt`
  - 映射GCP MySQL数据库中的channels表
  - 包含所有字段的完整映射
  - 支持Google Vertex AI JSON密钥存储

- **同步状态实体**: `ChannelSyncStatus.kt`
  - 记录同步进度和状态
  - 避免重复同步
  - 支持错误追踪

### 3. 数据访问层
- **ChannelRepository**: 查询GCP MySQL数据库
  - 增量同步查询（基于ID）
  - 状态过滤查询
  - 最大ID查询

- **ChannelSyncStatusRepository**: 管理同步状态
  - 按同步类型查询
  - 状态更新操作

### 4. 业务逻辑层
- **ChannelSyncService**: 核心同步逻辑
  - 增量同步实现
  - Google Vertex AI密钥验证
  - 服务账户JSON解析
  - 本地系统集成

### 5. 定时任务
- **ChannelSyncTimer**: 定时执行同步
  - 每30分钟自动同步
  - 支持手动触发
  - 完整的错误处理和日志记录

### 6. API接口
- **ChannelSyncController**: 手动控制接口
  - `POST /api/v1/sync/google-vertex-ai/trigger` - 手动触发同步
  - `GET /api/v1/sync/google-vertex-ai/status` - 获取同步状态

## 配置说明

### 数据库配置
在`application-*.yml`文件中添加MySQL数据源配置：

```yaml
spring:
  datasource:
    # 现有PostgreSQL配置...
    
    # MySQL数据源配置 - 用于同步GCP channels表
    mysql:
      url: ***********************************************************************************************
      username: ${MYSQL_USERNAME:gcp}
      password: ${MYSQL_PASSWORD:5BJnDPRLZ3zSDekw}
      driver-class-name: com.mysql.cj.jdbc.Driver
```

### 环境变量
- `MYSQL_USERNAME`: MySQL数据库用户名（默认：gcp）
- `MYSQL_PASSWORD`: MySQL数据库密码（默认：5BJnDPRLZ3zSDekw）

## 同步逻辑

### 增量同步流程
1. 查询上次同步的最大ID
2. 从GCP MySQL查询status=1且ID大于上次同步ID的记录
3. 验证密钥是否为Google Vertex AI类型
4. 解析服务账户JSON配置
5. 创建本地CommonTokenKey和GoogleVertexAITokenRefreshInfo记录
6. 更新同步状态

### 数据验证
- 验证JSON格式是否为有效的Google服务账户
- 检查必要字段：type, project_id, private_key, client_email等
- 提取项目ID、客户端邮箱等元数据

### 错误处理
- 单个记录失败不影响整体同步
- 详细的错误日志记录
- 同步状态追踪
- 支持重试机制

## 使用方法

### 自动同步
系统会每30分钟自动执行一次增量同步，无需手动干预。

### 手动同步
```bash
# 触发手动同步
curl -X POST http://localhost:8070/api/v1/sync/google-vertex-ai/trigger

# 查看同步状态
curl -X GET http://localhost:8070/api/v1/sync/google-vertex-ai/status
```

### 监控日志
查看应用日志中的同步相关信息：
```
Channel Sync | Incremental sync started
Channel Sync | Found X channels to sync
Channel Sync | Incremental sync completed | Success: Synced X channels
```

## 数据库迁移

运行以下迁移脚本创建同步状态表：
```sql
-- V4__create_channel_sync_status_table.sql
CREATE TABLE IF NOT EXISTS channel_sync_status (
    id BIGSERIAL PRIMARY KEY,
    sync_type VARCHAR(100) NOT NULL UNIQUE,
    last_sync_id BIGINT NOT NULL DEFAULT 0,
    last_sync_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sync_status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    error_message TEXT,
    sync_count INTEGER DEFAULT 0
);
```

## 依赖项

已添加MySQL驱动依赖：
```kotlin
implementation("com.mysql:mysql-connector-j")
```

## 注意事项

1. **数据安全**: 确保MySQL连接使用安全的网络环境
2. **性能考虑**: 大量数据同步时可能需要调整批处理大小
3. **错误恢复**: 同步失败时会记录错误信息，可通过日志排查问题
4. **重复处理**: 系统会自动避免重复同步相同的记录
5. **事务管理**: 使用独立的事务管理器确保数据一致性

## 扩展性

该同步框架设计为可扩展的，可以轻松添加其他类型的密钥同步：
- 修改`SYNC_TYPE`常量添加新的同步类型
- 创建对应的验证和解析逻辑
- 复用现有的同步状态管理机制
