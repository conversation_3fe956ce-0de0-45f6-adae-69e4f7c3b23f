import type { OrganizationModal } from "@/api/organization/organization-modal";
import type { UserProfile } from "@/api/user/user-model";
import { atom } from "jotai";



export type BusinessType = "TRAIL" | "SUBSCRIPTION" | "INNER";

export type PricingType = "YEARLY" | "QUARTERLY" | "MONTHLY" | "UNDEFINED";


export const userState = atom<UserProfile | null>(null);


export const organizationState = atom<OrganizationModal | null>(null);

