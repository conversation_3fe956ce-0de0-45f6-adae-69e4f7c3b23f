import { columnOrder<PERSON><PERSON> } from '@/state/table-state';
import { type ColumnFiltersState, type PaginationState, type SortingState, type VisibilityState } from '@tanstack/react-table';
import { useAtom } from 'jotai';
import React from 'react';

export function useTableState(columns: any[], isNeedSelect: boolean, tableId: string, defaultPageSize: number = 10) {
    const [columnOrderState] = useAtom(columnOrderAtom);
    const [columnOrder, setColumnOrder] = React.useState<string[]>(() =>
        isNeedSelect ? ['select', ...columns.map(c => c.id!)] : columns.map(c => c.id!)
    );

    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
        pageIndex: 0,
        pageSize: defaultPageSize,
    });
    const [rowSelection, setRowSelection] = React.useState({});

    React.useEffect(() => {
        if (columnOrderState[tableId]) {
            setColumnOrder(columnOrderState[tableId]);
        }
    }, [columnOrderState, tableId]);

    return {
        columnOrder,
        setColumnOrder,
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        pagination: { pageIndex, pageSize },
        setPagination,
        rowSelection,
        setRowSelection
    };
} 