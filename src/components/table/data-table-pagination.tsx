import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon, } from "@radix-ui/react-icons";
import { type Table } from "@tanstack/react-table";


interface DataTablePaginationProps<TData> {
    table: Table<TData>
}

export function DataTablePagination<TData>({
    table,
}: DataTablePaginationProps<TData>) {
    return (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-4 py-2">
            <div className="flex-1 text-sm text-muted-foreground text-center sm:text-left">
                已选择 {table.getFilteredSelectedRowModel().rows.length} 行 / 共{" "}
                {table.getFilteredRowModel().rows.length} 行
            </div>
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:space-x-6 lg:space-x-8">
                <div className="flex items-center space-x-2">
                    <p className="text-sm text-muted-foreground">每页行数</p>
                    <Select
                        value={`${table.getState().pagination.pageSize}`}
                        onValueChange={(value) => {
                            table.setPageSize(Number(value))
                        }}
                    >
                        <SelectTrigger className="h-8 w-[70px] sm:w-[100px]">
                            <SelectValue placeholder={table.getState().pagination.pageSize} />
                        </SelectTrigger>
                        <SelectContent side="top">
                            {[10, 20, 30, 50, 100].map((pageSize) => (
                                <SelectItem key={pageSize} value={`${pageSize}`}>
                                    {pageSize}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex w-[100px] items-center justify-center text-sm text-muted-foreground text-nowrap">
                    第 {table.getState().pagination.pageIndex + 1} 页 / 共 {table.getPageCount() === 0 ? 1 : table.getPageCount()} 页
                </div>
                <div className="flex items-center space-x-2">
                    <Button
                        variant="outline"
                        className="hidden sm:flex h-8 w-8 p-0"
                        onClick={() => table.setPageIndex(0)}
                        disabled={!table.getCanPreviousPage()}
                    >
                        <span className="sr-only">跳转到第一页</span>
                        <DoubleArrowLeftIcon className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="h-9 sm:h-8 w-9 sm:w-8 p-0"
                        onClick={() => table.previousPage()}
                        disabled={!table.getCanPreviousPage()}
                    >
                        <span className="sr-only">上一页</span>
                        <ChevronLeftIcon className="h-5 sm:h-4 w-5 sm:w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="h-9 sm:h-8 w-9 sm:w-8 p-0"
                        onClick={() => table.nextPage()}
                        disabled={!table.getCanNextPage()}
                    >
                        <span className="sr-only">下一页</span>
                        <ChevronRightIcon className="h-5 sm:h-4 w-5 sm:w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        className="hidden sm:flex h-8 w-8 p-0"
                        onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                        disabled={!table.getCanNextPage()}
                    >
                        <span className="sr-only">跳转到最后一页</span>
                        <DoubleArrowRightIcon className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    )
}