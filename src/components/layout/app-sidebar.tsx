"use client"

import * as React from "react"
import {
    Bot,
    Key,
    SquareTerminal,
    Upload,
    Users2,
    Wallet,
    Activity,
} from "lucide-react"


import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
} from "@/components/ui/sidebar"
import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"
import { TeamSwitcher } from "./team-switcher"
import { Separator } from "../ui/separator"
import { pushModal } from "../modals"

const data = {
    user: {
        name: "shadcn",
        email: "<EMAIL>",
        avatar: "/avatars/shadcn.jpg",
    },
    navMain: [
        {
            title: "仪表盘",
            url: "/dashboard/home",
            icon: SquareTerminal,
        },
        {
            title: "模型",
            url: "/dashboard/model",
            icon: Bot
        },
        {
            title: "团队",
            url: "/dashboard/team",
            icon: Users2
        },
        {
            title: "密钥管理",
            url: "/dashboard/key-management",
            icon: Key,
        },
        {
            title: "密钥监控",
            url: "/dashboard/key-monitoring",
            icon: Activity,
        },
        {
            title: "价格管理",
            url: "/dashboard/llm-price",
            icon: Wallet,
        },
    ],
    navSecondary: [
        {
            title: "创建组织",
            url: "#",
            icon: Upload,
            keyboardShortcut: "⌘ G",
            action: () => {
                pushModal('CreateOrganizationModal')
            }
        },
        // {
        //     title: "反馈",
        //     url: "#",
        //     icon: Send,
        // },
    ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    return (
        <Sidebar variant="inset" {...props}>
            <SidebarHeader>
                <TeamSwitcher />
            </SidebarHeader>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavSecondary items={data.navSecondary} className="mt-auto" />
            </SidebarContent>
            <Separator className="bg-transparent border-t border-dashed border-border" />
            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar >
    )
}
