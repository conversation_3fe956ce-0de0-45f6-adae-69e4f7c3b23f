import { Building2, Ellipsis, LogOut, Settings } from "lucide-react"
import React from "react"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useIsMobile } from "@/hooks/use-mobile"
import { organizationState, userState } from "@/state/user-state"
import { useAtomValue } from "jotai"
import { userApi } from "@/api/user/user-api"
import { SelectUserCommandMenu } from "../command/select-organization"

export function TeamSwitcher() {
    const isMobile = useIsMobile()
    const user = useAtomValue(userState);
    const [commandOpen, setCommandOpen] = React.useState(false);

    const organization = useAtomValue(organizationState);
    return (
        <>
            <SelectUserCommandMenu open={commandOpen} onOpenChange={setCommandOpen} />
            <DropdownMenu>
                <DropdownMenuTrigger className="w-full rounded-md ring-ring hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 data-[state=open]:bg-accent">
                    <div className="flex items-center gap-1.5 overflow-hidden px-2 py-1.5 text-left text-sm transition-all">
                        <div className="flex h-5 w-5 items-center justify-center rounded-sm bg-primary text-primary-foreground">
                            <div className="p-[1px] rounded-md bg-white border border-zinc-200 flex flex-row items-center justify-center">
                                <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders bg-zinc-100 h-6 w-6 rounded-md">
                                    <span className="aspect-square object-cover object-center rounded bg-ui-bg-component-hover text-black/80 pointer-events-none flex select-none items-center justify-center text-xs">
                                        {organization?.organizationName.slice(0, 1).toUpperCase()}
                                    </span>
                                </span>
                            </div>
                        </div>
                        <span className="font-medium pl-4 text-[13px]">{organization?.organizationName}</span>
                        <Ellipsis className="ml-auto h-4 w-4 text-muted-foreground/50" />
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    className="w-56"
                    align="start"
                    side={"bottom"}
                    sideOffset={8}
                    alignOffset={isMobile ? 8 : 0}
                >
                    <DropdownMenuLabel className="p-0 font-normal">
                        <div className="flex items-center gap-6 px-2 py-1.5 text-left text-sm">
                            <div className="flex h-5 w-5 items-center justify-center rounded-sm bg-primary text-primary-foreground">
                                <div className="p-[1px] rounded-md bg-white border border-zinc-200 flex flex-row items-center justify-center">
                                    <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders bg-zinc-100 h-6 w-6 rounded-md">
                                        <span className="aspect-square object-cover object-center rounded bg-ui-bg-component-hover text-black/80 pointer-events-none flex select-none items-center justify-center text-xs">
                                            {user?.userName.slice(0, 1).toUpperCase()}
                                        </span>
                                    </span>
                                </div>
                            </div>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium text-zinc-900">{user?.userName}</span>
                                <span className="truncate text-xs text-zinc-500">{user?.userEmail}</span>
                            </div>
                        </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                        <DropdownMenuItem onClick={() => setCommandOpen(true)}>
                            <Building2 className="mr-2 h-4 w-4" />
                            <span>切换组织</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                            <Settings className="mr-2 h-4 w-4" />
                            设置
                        </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => userApi.logout()}>
                        <LogOut className="mr-2 h-4 w-4" />
                        退出
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    )
}
