import React, { memo } from "react";
import { useLocation } from "react-router";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// 路径段到中文名称的映射表
const PATH_TO_CHINESE_NAME: Record<string, string> = {
    // 常见路径映射
    "": "首页",
    "home": "首页",
    "dashboard": "仪表盘",
    "organization": "组织管理",
    "model": "模型管理",
    "secret": "密钥管理",
    "subscribe": "订阅管理",
    "wallet": "钱包管理",
    "team": "团队管理",
};

function RawAppBreadcrumb() {
    const { pathname } = useLocation();

    // 处理路径生成面包屑导航
    const generateBreadcrumbs = () => {
        // 移除第一个斜杠并分割路径
        const pathSegments = pathname.replace(/^\//, '').split('/').filter(Boolean);

        // 如果路径为空，则显示首页
        if (pathSegments.length === 0) {
            return (
                <BreadcrumbItem>
                    <BreadcrumbPage>{PATH_TO_CHINESE_NAME[""]}</BreadcrumbPage>
                </BreadcrumbItem>
            );
        }

        return pathSegments.map((segment: string, index: number) => {
            // 构建当前段的完整路径
            const path = '/' + pathSegments.slice(0, index + 1).join('/');

            // 获取中文名称，如果映射表中不存在则使用格式化后的段名
            const formattedSegment = PATH_TO_CHINESE_NAME[segment] ||
                (segment.charAt(0).toUpperCase() + segment.slice(1));

            // 最后一个段落使用 BreadcrumbPage
            if (index === pathSegments.length - 1) {
                return (
                    <BreadcrumbItem key={path}>
                        <BreadcrumbPage className="!text-zinc-500">{formattedSegment}</BreadcrumbPage>
                    </BreadcrumbItem>
                );
            }

            // 其他段落使用 BreadcrumbLink
            return (
                <React.Fragment key={path}>
                    <BreadcrumbItem>
                        <BreadcrumbLink href={path}>{formattedSegment}</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                </React.Fragment>
            );
        });
    };

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {generateBreadcrumbs()}
            </BreadcrumbList>
        </Breadcrumb>
    );
}


export const AppBreadcrumb = memo(RawAppBreadcrumb)