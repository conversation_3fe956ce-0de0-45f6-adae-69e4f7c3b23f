import { organizationApi } from "@/api/organization/organization-api";
import { userApi } from "@/api/user/user-api";
import { Loading } from "@/components/ui/loading";
import { organizationState, userState } from "@/state/user-state";
import { useSet<PERSON>tom } from "jotai";
import { type ReactNode, useEffect, useState } from "react";
import { toast } from "sonner";


interface AuthProviderProps {
    children: ReactNode
}


export default function AuthProvider({ children }: AuthProviderProps) {
    const setUser = useSetAtom(userState);
    const setOrganization = useSetAtom(organizationState);
    const [isInitializing, setIsInitializing] = useState(true);


    useEffect(() => {
        Promise.all([
            userApi.profile().then(setUser),
            organizationApi.getCurrentOrganization().then(setOrganization),
        ]).catch((error) => {
            toast.error('获取用户信息失败: ' + error.message)
        }).finally(() => {
            setIsInitializing(false)
        });
    }, []);


    if (isInitializing) {
        return <Loading />
    }

    return (
        <>{children}</>
    )
}