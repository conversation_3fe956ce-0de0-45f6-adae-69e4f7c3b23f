"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { useState, useEffect } from "react"
import { Link, useLocation } from "react-router"

import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuAction,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export function NavMain({
    items,
}: {
    items: {
        title: string
        url: string
        icon: LucideIcon
        isActive?: boolean
        items?: {
            title: string
            url: string
        }[]
    }[]
}) {

    const { pathname } = useLocation()
    const [openItems, setOpenItems] = useState<string[]>([])

    useEffect(() => {
        // 找出哪些菜单项应该展开 (如果当前路径匹配其子项)
        const itemsToOpen = items
            .filter(item =>
                item.url === pathname ||
                item.items?.some(subItem => subItem.url === pathname)
            )
            .map(item => item.title)

        setOpenItems(itemsToOpen)
    }, [pathname, items])

    return (
        <SidebarGroup>
            <SidebarGroupLabel>平台</SidebarGroupLabel>
            <SidebarMenu>
                {items.map((item) => {
                    const isItemActive = item.url === pathname
                    const isOpen = openItems.includes(item.title)

                    return (
                        <Collapsible
                            key={item.title}
                            asChild
                            open={isOpen}
                            onOpenChange={(open) => {
                                if (open) {
                                    setOpenItems([...openItems, item.title])
                                } else {
                                    setOpenItems(openItems.filter(title => title !== item.title))
                                }
                            }}
                        >
                            <SidebarMenuItem>
                                <SidebarMenuButton asChild tooltip={item.title} isActive={isItemActive}>
                                    <Link to={item.url}>
                                        <item.icon />
                                        <span className="hover:text-black">{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>
                                {item.items?.length ? (
                                    <>
                                        <CollapsibleTrigger asChild>
                                            <SidebarMenuAction className="data-[state=open]:rotate-90">
                                                <ChevronRight />
                                                <span className="sr-only">Toggle</span>
                                            </SidebarMenuAction>
                                        </CollapsibleTrigger>
                                        <CollapsibleContent>
                                            <SidebarMenuSub>
                                                {item.items?.map((subItem) => (
                                                    <SidebarMenuSubItem key={subItem.title}>
                                                        <SidebarMenuSubButton asChild isActive={subItem.url === pathname}>
                                                            <Link to={subItem.url}>
                                                                <span>{subItem.title}</span>
                                                            </Link>
                                                        </SidebarMenuSubButton>
                                                    </SidebarMenuSubItem>
                                                ))}
                                            </SidebarMenuSub>
                                        </CollapsibleContent>
                                    </>
                                ) : null}
                            </SidebarMenuItem>

                        </Collapsible>
                    )
                })}
                {/* <SidebarMenuItem>
                    <SidebarMenuButton className="text-sidebar-foreground/70" onClick={() => {
                    }}>
                        <div className="flex flex-row justify-between w-full items-center">
                            <div className="flex flex-row items-center gap-1">
                                <Upload className="text-sidebar-foreground/70 h-4 w-4 " />
                                <span>导入订单</span>
                            </div>
                            <Badge variant='outline' className="text-xs text-sidebar-foreground/70">
                                ⌘ K
                            </Badge>
                        </div>
                    </SidebarMenuButton>
                </SidebarMenuItem> */}
            </SidebarMenu>
        </SidebarGroup>
    )
}
