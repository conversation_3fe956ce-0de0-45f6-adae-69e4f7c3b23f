import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
    Key, 
    CheckCircle, 
    XCircle, 
    AlertTriangle, 
    TrendingUp, 
    Activity,
    Layers,
    BarChart3
} from "lucide-react";
import type { KeyMonitoringOverview } from "@/api/key-monitoring/key-monitoring-types";

interface KeyOverviewCardsProps {
    overview: KeyMonitoringOverview;
}

export default function KeyOverviewCards({ overview }: KeyOverviewCardsProps) {
    const successRateColor = overview.avgSuccessRate >= 0.9 ? "text-green-600" : 
                             overview.avgSuccessRate >= 0.7 ? "text-yellow-600" : "text-red-600";
    
    const successRateBgColor = overview.avgSuccessRate >= 0.9 ? "bg-green-50" : 
                               overview.avgSuccessRate >= 0.7 ? "bg-yellow-50" : "bg-red-50";

    return (
        <div className="space-y-6">
            {/* 主要指标卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 总密钥数 */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">总密钥数</CardTitle>
                        <Key className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{overview.totalKeys}</div>
                        <p className="text-xs text-muted-foreground">
                            活跃: {overview.activeKeys} | 禁用: {overview.disabledKeys}
                        </p>
                    </CardContent>
                </Card>

                {/* 平均成功率 */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">平均成功率</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className={`text-2xl font-bold ${successRateColor}`}>
                            {(overview.avgSuccessRate * 100).toFixed(1)}%
                        </div>
                        <Progress 
                            value={overview.avgSuccessRate * 100} 
                            className="mt-2"
                        />
                    </CardContent>
                </Card>

                {/* 总请求数 */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">总请求数</CardTitle>
                        <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {overview.totalRequests.toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            累计处理请求
                        </p>
                    </CardContent>
                </Card>

                {/* 模型数量 */}
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">支持模型</CardTitle>
                        <Layers className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{overview.totalModels}</div>
                        <p className="text-xs text-muted-foreground">
                            已配置模型数量
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* 密钥状态分布 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <BarChart3 className="w-5 h-5" />
                            密钥状态分布
                        </CardTitle>
                        <CardDescription>
                            各状态密钥的数量分布
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span className="text-sm">活跃</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{overview.activeKeys}</span>
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                    {((overview.activeKeys / overview.totalKeys) * 100).toFixed(1)}%
                                </Badge>
                            </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <XCircle className="w-4 h-4 text-gray-600" />
                                <span className="text-sm">禁用</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{overview.disabledKeys}</span>
                                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                                    {((overview.disabledKeys / overview.totalKeys) * 100).toFixed(1)}%
                                </Badge>
                            </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <AlertTriangle className="w-4 h-4 text-red-600" />
                                <span className="text-sm">认证失败</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{overview.authFailedKeys}</span>
                                <Badge variant="secondary" className="bg-red-100 text-red-800">
                                    {((overview.authFailedKeys / overview.totalKeys) * 100).toFixed(1)}%
                                </Badge>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* 渠道分布 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Key className="w-5 h-5" />
                            渠道分布
                        </CardTitle>
                        <CardDescription>
                            各渠道密钥的数量分布
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {Object.entries(overview.channelDistribution)
                            .sort(([,a], [,b]) => b - a)
                            .map(([channel, count]) => (
                            <div key={channel} className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                                    <span className="text-sm">{channel}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">{count}</span>
                                    <Badge variant="outline">
                                        {((count / overview.totalKeys) * 100).toFixed(1)}%
                                    </Badge>
                                </div>
                            </div>
                        ))}
                    </CardContent>
                </Card>
            </div>

            {/* 更新时间 */}
            <div className="text-xs text-muted-foreground text-center">
                最后更新: {new Date(overview.lastUpdateTime).toLocaleString()}
            </div>
        </div>
    );
}
