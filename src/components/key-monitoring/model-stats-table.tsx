import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { 
    ChevronDown, 
    ChevronRight, 
    Key, 
    TrendingUp, 
    TrendingDown,
    Settings,
    Star,
    AlertTriangle
} from "lucide-react";
import type { ModelKeyStats } from "@/api/key-monitoring/key-monitoring-types";

interface ModelStatsTableProps {
    modelsStats: Record<string, ModelKeyStats>;
}

export default function ModelStatsTable({ modelsStats }: ModelStatsTableProps) {
    const [expandedModels, setExpandedModels] = useState<Set<string>>(new Set());

    const toggleModel = (modelName: string) => {
        const newExpanded = new Set(expandedModels);
        if (newExpanded.has(modelName)) {
            newExpanded.delete(modelName);
        } else {
            newExpanded.add(modelName);
        }
        setExpandedModels(newExpanded);
    };

    const getSuccessRateColor = (rate: number) => {
        if (rate >= 0.9) return "text-green-600";
        if (rate >= 0.7) return "text-yellow-600";
        return "text-red-600";
    };

    const getSuccessRateBadge = (rate: number) => {
        if (rate >= 0.9) return "bg-green-100 text-green-800";
        if (rate >= 0.7) return "bg-yellow-100 text-yellow-800";
        return "bg-red-100 text-red-800";
    };

    return (
        <div className="space-y-4">
            {Object.entries(modelsStats)
                .sort(([,a], [,b]) => b.totalRequests - a.totalRequests)
                .map(([modelName, stats]) => (
                <Card key={modelName} className="overflow-hidden">
                    <CardHeader 
                        className="cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => toggleModel(modelName)}
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                {expandedModels.has(modelName) ? 
                                    <ChevronDown className="w-4 h-4" /> : 
                                    <ChevronRight className="w-4 h-4" />
                                }
                                <div>
                                    <CardTitle className="text-base">{modelName}</CardTitle>
                                    <CardDescription>
                                        {stats.keyCount} 个密钥 • {stats.totalRequests.toLocaleString()} 次请求
                                    </CardDescription>
                                </div>
                            </div>
                            <div className="flex items-center gap-4">
                                <Badge className={getSuccessRateBadge(stats.avgSuccessRate)}>
                                    {(stats.avgSuccessRate * 100).toFixed(1)}%
                                </Badge>
                                <div className="flex items-center gap-1">
                                    <Key className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">{stats.keyCount}</span>
                                </div>
                            </div>
                        </div>
                    </CardHeader>

                    {expandedModels.has(modelName) && (
                        <CardContent className="pt-0">
                            <div className="space-y-4">
                                {/* 调度器配置 */}
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                                        <Settings className="w-4 h-4" />
                                        调度器配置
                                    </h4>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span className="text-muted-foreground">探索率:</span>
                                            <span className="ml-2 font-medium">{stats.schedulerConfig.epsilon}</span>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">错误阈值:</span>
                                            <span className="ml-2 font-medium">{stats.schedulerConfig.errThreshold}</span>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">窗口大小:</span>
                                            <span className="ml-2 font-medium">{stats.schedulerConfig.windowSize}</span>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">最小样本:</span>
                                            <span className="ml-2 font-medium">{stats.schedulerConfig.minSampleSize}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* 密钥详情 */}
                                <div>
                                    <h4 className="text-sm font-medium mb-3">密钥详情</h4>
                                    <div className="space-y-2">
                                        {Object.entries(stats.keyStats)
                                            .sort(([,a], [,b]) => b.score - a.score)
                                            .map(([keyId, keyStats]) => (
                                            <div 
                                                key={keyId} 
                                                className="flex items-center justify-between p-3 bg-white border rounded-lg hover:bg-gray-50"
                                            >
                                                <div className="flex items-center gap-3">
                                                    <div className="flex items-center gap-2">
                                                        {stats.bestKeyId?.toString() === keyId && (
                                                            <Star className="w-4 h-4 text-yellow-500" />
                                                        )}
                                                        {keyStats.errorRate > 0.5 && (
                                                            <AlertTriangle className="w-4 h-4 text-red-500" />
                                                        )}
                                                        <span className="text-sm font-medium">
                                                            密钥 {keyId}
                                                        </span>
                                                    </div>
                                                    <Badge variant="outline" className="text-xs">
                                                        {keyStats.channel}
                                                    </Badge>
                                                    {keyStats.name && (
                                                        <span className="text-xs text-muted-foreground">
                                                            {keyStats.name}
                                                        </span>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-4 text-sm">
                                                    <div className="text-center">
                                                        <div className="text-xs text-muted-foreground">成功率</div>
                                                        <div className={getSuccessRateColor(keyStats.successRate)}>
                                                            {(keyStats.successRate * 100).toFixed(1)}%
                                                        </div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-xs text-muted-foreground">权重</div>
                                                        <div className="font-medium">{keyStats.weight.toFixed(2)}</div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-xs text-muted-foreground">得分</div>
                                                        <div className="font-medium">{keyStats.score.toFixed(3)}</div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-xs text-muted-foreground">请求数</div>
                                                        <div className="font-medium">{keyStats.requestCount.toLocaleString()}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    )}
                </Card>
            ))}
        </div>
    );
}
