import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    AreaChart,
    Area,
    BarChart,
    Bar
} from "recharts";
import { TrendingUp, BarChart3, Activity } from "lucide-react";
import type { KeyPerformanceTrend } from "@/api/key-monitoring/key-monitoring-types";

interface KeyPerformanceChartsProps {
    trends: KeyPerformanceTrend[];
}

export default function KeyPerformanceCharts({ trends }: KeyPerformanceChartsProps) {
    const [selectedModel, setSelectedModel] = useState<string>("all");
    const [chartType, setChartType] = useState<"success" | "requests" | "errors">("success");

    // 获取所有模型名称
    const models = Array.from(new Set(trends.map(t => t.modelName))).sort();

    // 过滤数据
    const filteredTrends = selectedModel === "all"
        ? trends
        : trends.filter(t => t.modelName === selectedModel);

    // 按时间聚合数据
    const aggregatedData = filteredTrends.reduce((acc, trend) => {
        const timeKey = trend.timestamp.substring(0, 16); // 精确到分钟

        if (!acc[timeKey]) {
            acc[timeKey] = {
                timestamp: timeKey,
                totalRequests: 0,
                totalSuccessRate: 0,
                totalErrorRate: 0,
                count: 0,
                models: new Set()
            };
        }

        acc[timeKey].totalRequests += trend.requestCount;
        acc[timeKey].totalSuccessRate += trend.successRate;
        acc[timeKey].totalErrorRate += trend.errorRate;
        acc[timeKey].count += 1;
        acc[timeKey].models.add(trend.modelName);

        return acc;
    }, {} as Record<string, any>);

    // 转换为图表数据
    const chartData = Object.values(aggregatedData)
        .map((item: any) => ({
            time: new Date(item.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            }),
            timestamp: item.timestamp,
            successRate: Number((item.totalSuccessRate / item.count * 100).toFixed(1)),
            errorRate: Number((item.totalErrorRate / item.count * 100).toFixed(1)),
            requests: item.totalRequests,
            modelCount: item.models.size
        }))
        .sort((a, b) => a.timestamp.localeCompare(b.timestamp))
        .slice(-24); // 只显示最近24个数据点

    // 计算模型统计数据
    const modelStats = models.map(model => {
        const modelTrends = trends.filter(t => t.modelName === model);
        const avgSuccessRate = modelTrends.length > 0
            ? modelTrends.reduce((sum, t) => sum + t.successRate, 0) / modelTrends.length * 100
            : 0;
        const totalRequests = modelTrends.reduce((sum, t) => sum + t.requestCount, 0);

        return {
            model: model.length > 15 ? model.substring(0, 15) + "..." : model,
            fullModel: model,
            successRate: Number(avgSuccessRate.toFixed(1)),
            requests: totalRequests
        };
    });

    return (
        <div className="space-y-6">
            {/* 控制面板 */}
            <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                    <label className="text-sm font-medium">模型:</label>
                    <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger className="w-40">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">所有模型</SelectItem>
                            {models.map(model => (
                                <SelectItem key={model} value={model}>{model}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                
                <div className="flex items-center gap-2">
                    <label className="text-sm font-medium">图表类型:</label>
                    <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="success">成功率</SelectItem>
                            <SelectItem value="requests">请求量</SelectItem>
                            <SelectItem value="errors">错误率</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            {/* 图表区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 主要趋势图 */}
                <Card className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="w-5 h-5" />
                            {chartType === "success" ? "成功率趋势" :
                             chartType === "requests" ? "请求量趋势" : "错误率趋势"}
                        </CardTitle>
                        <CardDescription>
                            {selectedModel === "all" ? "所有模型" : selectedModel} 的性能趋势
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                            <AreaChart data={chartData}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="time" />
                                <YAxis />
                                <Tooltip
                                    labelFormatter={(label) => `时间: ${label}`}
                                    formatter={(value, name) => [
                                        `${value}${chartType === "requests" ? "" : "%"}`,
                                        name === "successRate" ? "成功率" :
                                        name === "errorRate" ? "错误率" : "请求数"
                                    ]}
                                />
                                <Legend />
                                {chartType === "success" && (
                                    <Area
                                        type="monotone"
                                        dataKey="successRate"
                                        stroke="#82ca9d"
                                        fill="#82ca9d"
                                        fillOpacity={0.3}
                                        name="成功率"
                                    />
                                )}
                                {chartType === "requests" && (
                                    <Area
                                        type="monotone"
                                        dataKey="requests"
                                        stroke="#8884d8"
                                        fill="#8884d8"
                                        fillOpacity={0.3}
                                        name="请求数"
                                    />
                                )}
                                {chartType === "errors" && (
                                    <Area
                                        type="monotone"
                                        dataKey="errorRate"
                                        stroke="#ff7300"
                                        fill="#ff7300"
                                        fillOpacity={0.3}
                                        name="错误率"
                                    />
                                )}
                            </AreaChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>

                {/* 模型对比图 */}
                {selectedModel === "all" && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="w-5 h-5" />
                                模型对比
                            </CardTitle>
                            <CardDescription>
                                各模型平均性能对比
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ResponsiveContainer width="100%" height={250}>
                                <BarChart data={modelStats}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="model" angle={-45} textAnchor="end" height={80} />
                                    <YAxis />
                                    <Tooltip
                                        formatter={(value, name) => [
                                            `${value}%`,
                                            "成功率"
                                        ]}
                                    />
                                    <Bar dataKey="successRate" fill="#82ca9d" />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                )}

                {/* 实时状态 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Activity className="w-5 h-5" />
                            实时状态
                        </CardTitle>
                        <CardDescription>
                            当前性能指标
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {chartData.length > 0 ? (
                            <>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">当前成功率</span>
                                    <Badge className="bg-green-100 text-green-800">
                                        {chartData[chartData.length - 1]?.successRate}%
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">当前请求量</span>
                                    <Badge variant="outline">
                                        {chartData[chartData.length - 1]?.requests}
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">活跃模型数</span>
                                    <Badge variant="outline">
                                        {chartData[chartData.length - 1]?.modelCount}
                                    </Badge>
                                </div>
                            </>
                        ) : (
                            <div className="text-center text-muted-foreground">
                                暂无数据
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
