import { createPushModal } from 'pushmodal'
import { Wrapper } from './dynamic'
import { CreateOrganizationModal } from './organization/create-organization-modal'
import { UpdateOrganizationModal } from './organization/update-organization-modal'
import { EditModelModal } from './edit-model/edit-model-modal'
import { CreateSecretModal } from './secret/create-secret-modal'
import { DoubleCheckModal } from './double-check-modal'
import { AddTeamMemberModal } from './team/add-team-member-modal'
import { UserWalletPreviewModal } from './user-wallet/user-wallet-preview-modal'
import WalletBillModal from './wallet/wallet-bill-modal'
import CreateKeyModal from './key-management/create-common-key-modal-v2'
import { EditCommonKeyModal } from './key-management/edit-common-key-modal'
import { TestCommonKeyModal } from './key-management/test-common-key-modal'
import { CreateLlmPriceModal } from './llm-price/create-llm-price-modal'
import { EditLlmPriceModal } from './llm-price/edit-llm-price-modal'
import { ViewLlmPriceModal } from './llm-price/view-llm-price-modal'


/**
 * https://github.com/lindesvard/pushmodal
 */

export const {
    pushModal,
    popModal,
    popAllModals,
    replaceWithModal,
    useOnPushModal,
    onPushModal,
    ModalProvider
} = createPushModal({
    modals: {
        "CreateOrganizationModal": {
            Component: CreateOrganizationModal,
            Wrapper: Wrapper,
        },
        "UpdateOrganizationModal": {
            Component: UpdateOrganizationModal,
            Wrapper: Wrapper,
        },
        "EditModelModal": {
            Component: EditModelModal,
            Wrapper: Wrapper,
        },
        "DoubleCheckModal": {
            Component: DoubleCheckModal,
            Wrapper: Wrapper,
        },
        "CreateSecretModal": {
            Component: CreateSecretModal,
            Wrapper: Wrapper,
        },
        "AddTeamMemberModal": {
            Component: AddTeamMemberModal,
            Wrapper: Wrapper,
        },
        "UserWalletPreviewModal": {
            Component: UserWalletPreviewModal,
            Wrapper: Wrapper,
        },
        "WalletBillModal": {
            Component: WalletBillModal,
            Wrapper: Wrapper,
        },
        "CreateKeyModal": {
            Component: CreateKeyModal,
            Wrapper: Wrapper,
        },
        "EditKeyModal": {
            Component: EditCommonKeyModal,
            Wrapper: Wrapper,
        },
        "TestKeyModal": {
            Component: TestCommonKeyModal,
            Wrapper: Wrapper,
        },
        "CreateLlmPriceModal": {
            Component: CreateLlmPriceModal,
            Wrapper: Wrapper,
        },
        "EditLlmPriceModal": {
            Component: EditLlmPriceModal,
            Wrapper: Wrapper,
        },
        "ViewLlmPriceModal": {
            Component: ViewLlmPriceModal,
            Wrapper: Wrapper,
        },
        "AlertModal": {
            Component: DoubleCheckModal,
            Wrapper: Wrapper,
        },
    },
})
