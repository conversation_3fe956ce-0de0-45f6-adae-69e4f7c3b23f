"use client"

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/new/dialog";
import CommonButton from "@/components/ui/button/new-button";
import { useRequest } from "ahooks";
import { commonKeyApi } from "@/api/key-management/common-key-api";
import { toast } from "sonner";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Badge } from "@/components/ui/badge";
import { X, Check, Upload, FileJson } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { systemModelApi } from "@/api/system-model/system-model-api";
import type { KeyChannel, GoogleServiceAccountJson } from "@/api/key-management/common-key-model";
import StatusIcon from "@/components/icons/status-icon";
import type { SystemModelResponse } from "@/api/system-model/system-model-model";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useFileUpload } from "@/hooks/use-file-upload";
import { useCallback } from "react";

interface ModelGroup {
    id: string;
    vendor: string;
    models: {
        id: string;
        name: string;
        logoUrl?: string;
    }[];
}

// 基础表单 schema
const baseSchema = z.object({
    name: z.string().optional(),
    accessToken: z.string().optional(),
    domain: z.string().optional(),
    autoDisable: z.boolean().optional(),
    modelMapping: z.string().optional(),
    quota: z.number().optional(),
    // 调度器配置字段
    weight: z.number().min(0.1).max(10.0).optional(),
    windowSize: z.number().min(10).max(1000).optional(),
    epsilon: z.number().min(0.01).max(0.5).optional(),
    errThreshold: z.number().min(0.1).max(0.9).optional(),
});

// Claude Code 专用 schema
const claudeCodeSchema = baseSchema.extend({
    refreshToken: z.string().min(1, "刷新令牌不能为空"),
    clientId: z.string().min(1, "客户端ID不能为空"),
    email: z.string().email().optional().or(z.literal('')),
    expiresAt: z.string().optional(),
    accountType: z.enum(['PRO', 'MAX_100', 'MAX_200'] as const),
});

// 通用密钥 schema
const commonKeySchema = baseSchema.extend({
    accessToken: z.string().min(1, "访问令牌不能为空"),
});

/**
 * 将后端系统模型数据转换为前端需要的格式并按厂商分组
 */
function transformAndGroupModels(systemModels: SystemModelResponse[]): ModelGroup[] {
    // 创建一个Set来跟踪已使用的唯一标识符，确保唯一性
    const usedIds = new Set<string>();

    // 按厂商分组
    const groupedByManufacturer = systemModels.reduce((acc, model, index) => {
        const manufacturer = model.manufacturerName || "未知厂商"
        if (!acc[manufacturer]) {
            acc[manufacturer] = []
        }

        const modelName = model.presentationModel || model.modelName || "未知模型";
        const manufacturerSlug = manufacturer.toLowerCase().replace(/\s+/g, '-');

        // 使用厂商名称+模型名称生成唯一标识符
        let uniqueId = `${manufacturerSlug}-${modelName.toLowerCase().replace(/\s+/g, '-')}`;

        // 如果仍然重复，添加数组索引
        if (usedIds.has(uniqueId)) {
            uniqueId = `${uniqueId}-${index}`;
        }

        // 如果还是重复，添加时间戳
        if (usedIds.has(uniqueId)) {
            uniqueId = `${uniqueId}-${Date.now()}`;
        }

        // 最后保险，添加随机数
        let counter = 1;
        let finalId = uniqueId;
        while (usedIds.has(finalId)) {
            finalId = `${uniqueId}-${Math.random().toString(36).substr(2, 9)}-${counter}`;
            counter++;
        }

        usedIds.add(finalId);

        // 添加调试日志
        if (process.env.NODE_ENV === 'development') {
            console.log(`Generated unique ID: ${finalId} for model: ${modelName}`);
        }

        const transformedModel = {
            id: finalId,
            name: modelName,
            logoUrl: model.logoUrl
        }

        acc[manufacturer].push(transformedModel)
        return acc
    }, {} as Record<string, any[]>)

    // 转换为 ModelGroup 数组
    return Object.entries(groupedByManufacturer).map(([manufacturer, models]) => ({
        id: manufacturer.toLowerCase().replace(/\s+/g, '-'),
        vendor: manufacturer,
        models: models
    }))
}

const CreateKeyModal = ({
    onClose = () => { },
    onSuccess = () => { }
}: {
    onClose?: () => void;
    onSuccess?: () => void;
}) => {
    const [open, setOpen] = useState(true);
    const [activeTab, setActiveTab] = useState<"details" | "models" | "advanced">("details");
    const [selectedModelIds, setSelectedModelIds] = useState<string[]>([]);
    const [modelGroups, setModelGroups] = useState<ModelGroup[]>([]);
    const [keyType, setKeyType] = useState<KeyChannel>("ClaudeCode");
    const [googleServiceAccountJson, setGoogleServiceAccountJson] = useState<string>("");
    const [parsedServiceAccount, setParsedServiceAccount] = useState<GoogleServiceAccountJson | null>(null);
    const [parseError, setParseError] = useState<string>("");

    const currentSchema = keyType === 'ClaudeCode' ? claudeCodeSchema : commonKeySchema;

    const form = useForm<any>({
        resolver: (zodResolver as any)(currentSchema),
        defaultValues: {
            name: "",
            accessToken: "",
            refreshToken: "",
            clientId: "",
            email: "",
            expiresAt: "",
            accountType: "PRO",
            domain: "",
            autoDisable: false,
            modelMapping: "",
            // 调度器配置默认值
            weight: 1.0,
            windowSize: 100,
            epsilon: 0.05,
            errThreshold: 0.5,
        }
    });

    // 当 keyType 改变时重置表单
    useEffect(() => {
        form.reset({
            name: "",
            accessToken: "",
            refreshToken: "",
            clientId: "",
            email: "",
            expiresAt: "",
            accountType: "PRO",
            domain: "",
            autoDisable: false,
            modelMapping: "",
            // 调度器配置默认值
            weight: 1.0,
            windowSize: 100,
            epsilon: 0.05,
            errThreshold: 0.5,
        });
    }, [keyType, form]);

    // 获取模型列表
    const { loading: modelsLoading } = useRequest(
        systemModelApi.getDisplayModels,
        {
            onSuccess: (data: SystemModelResponse[]) => {
                const groupedModels = transformAndGroupModels(data)
                setModelGroups(groupedModels)
            },
            onError: (error) => {
                toast.error(`获取模型列表失败: ${error.message}`)
            }
        }
    );

    // 创建密钥
    // 检测是否为批量输入
    const detectBatchInput = (data: any) => {
        if (keyType === "GoogleVertexAI") {
            // Google Vertex AI: 检查是否有多个文件上传
            return fileUploadState.files.length > 1;
        } else if (keyType === "ClaudeCode") {
            // Claude Code: 检查refreshToken是否包含换行符
            return data.refreshToken && data.refreshToken.includes('\n');
        } else {
            // 其他类型: 检查accessToken是否包含换行符
            return data.accessToken && data.accessToken.includes('\n');
        }
    };

    // 解析批量输入
    const parseBatchInput = (data: any) => {
        if (keyType === "GoogleVertexAI") {
            // Google Vertex AI: 每个文件一个密钥
            return fileUploadState.files.map((_, index) => ({
                ...data,
                name: data.name ? `${data.name} ${index + 1}` : `Google Vertex AI Key ${index + 1}`
            }));
        } else if (keyType === "ClaudeCode") {
            // Claude Code: 按行分割refreshToken
            const lines = data.refreshToken.split('\n').filter((line: string) => line.trim());
            return lines.map((line: string, index: number) => {
                const parts = line.split('|');
                return {
                    ...data,
                    refreshToken: parts[0]?.trim() || line.trim(),
                    clientId: parts.length > 1 ? parts[1]?.trim() : data.clientId,
                    email: parts.length > 2 ? parts[2]?.trim() : data.email,
                    accountType: parts.length > 3 ? parts[3]?.trim() : data.accountType,
                    name: parts.length > 4 ? parts[4]?.trim() : (data.name ? `${data.name} ${index + 1}` : `Claude Code Key ${index + 1}`)
                };
            });
        } else {
            // 其他类型: 按行分割accessToken
            const lines = data.accessToken.split('\n').filter((line: string) => line.trim());
            return lines.map((line: string, index: number) => {
                const parts = line.split('|');
                return {
                    ...data,
                    accessToken: parts[0]?.trim() || line.trim(),
                    domain: parts.length > 1 ? parts[1]?.trim() : data.domain,
                    name: parts.length > 2 ? parts[2]?.trim() : (data.name ? `${data.name} ${index + 1}` : `${keyType} Key ${index + 1}`)
                };
            });
        }
    };

    const { run: createKey, loading } = useRequest(
        async (data: any) => {
            console.log("Creating key with type:", keyType, "data:", data);

            // 检测是否为批量输入
            const isBatch = detectBatchInput(data);

            if (isBatch) {
                console.log("Detected batch input, creating multiple keys...");
                const batchData = parseBatchInput(data);

                if (keyType === "ClaudeCode") {
                    const result = await commonKeyApi.createClaudeCodeKeysBatch({ keys: batchData });
                    const successCount = result.successful.length;
                    const failCount = result.failed.length;

                    if (failCount === 0) {
                        toast.success(`批量创建成功！共创建 ${successCount} 个密钥`);
                    } else {
                        toast.warning(`批量创建完成：成功 ${successCount} 个，失败 ${failCount} 个`);
                        // 显示失败详情
                        result.failed.forEach(failure => {
                            console.error(`第 ${failure.index + 1} 个密钥创建失败:`, failure.error);
                        });
                    }
                } else if (keyType === "GoogleVertexAI") {
                    // Google Vertex AI 批量处理：逐个导入每个文件
                    let successCount = 0;
                    let failCount = 0;

                    // 将唯一ID转换为模型名称
                    const modelNames = selectedModelIds.map(uniqueId => {
                        // 查找对应的模型名称
                        for (const group of modelGroups) {
                            const model = group.models.find(m => m.id === uniqueId);
                            if (model) {
                                return model.name;
                            }
                        }
                        return uniqueId; // 如果找不到，使用原ID作为备用
                    });

                    for (let i = 0; i < fileUploadState.files.length; i++) {
                        try {
                            const file = fileUploadState.files[i].file;
                            if (file instanceof File) {
                                const content = await file.text();

                                const importRequest = {
                                    serviceAccountJson: content,
                                    name: data.name ? `${data.name} ${i + 1}` : undefined,
                                    supportModels: modelNames,
                                    autoDisable: data.autoDisable || false,
                                    modelMapping: data.modelMapping,
                                    quota: data.quota ? parseFloat(data.quota) : undefined,
                                    weight: data.weight ? parseFloat(data.weight) : 1.0,
                                    windowSize: data.windowSize ? parseInt(data.windowSize) : 100,
                                    epsilon: data.epsilon ? parseFloat(data.epsilon) : 0.05,
                                    errThreshold: data.errThreshold ? parseFloat(data.errThreshold) : 0.5
                                };

                                await commonKeyApi.importGoogleVertexAI(importRequest);
                                successCount++;
                            }
                        } catch (error) {
                            console.error(`第 ${i + 1} 个文件导入失败:`, error);
                            failCount++;
                        }
                    }

                    if (failCount === 0) {
                        toast.success(`批量创建成功！共创建 ${successCount} 个密钥`);
                    } else {
                        toast.warning(`批量创建完成：成功 ${successCount} 个，失败 ${failCount} 个`);
                    }
                } else {
                    const result = await commonKeyApi.createKeysBatch({ keys: batchData });
                    const successCount = result.successful.length;
                    const failCount = result.failed.length;

                    if (failCount === 0) {
                        toast.success(`批量创建成功！共创建 ${successCount} 个密钥`);
                    } else {
                        toast.warning(`批量创建完成：成功 ${successCount} 个，失败 ${failCount} 个`);
                        // 显示失败详情
                        result.failed.forEach(failure => {
                            console.error(`第 ${failure.index + 1} 个密钥创建失败:`, failure.error);
                        });
                    }
                }
            } else {
                // 单个密钥创建逻辑保持不变
                if (keyType === "ClaudeCode") {
                    console.log("Creating Claude Code key...");
                    await commonKeyApi.createClaudeCodeKey(data);
                } else if (keyType === "GoogleVertexAI") {
                    // 使用导入接口，传递完整参数
                    console.log("Importing Google Vertex AI key with JSON and parameters:", {
                        hasJson: !!googleServiceAccountJson,
                        selectedModels: selectedModelIds.length,
                        data: data
                    });

                    // 将唯一ID转换为模型名称
                    const modelNames = selectedModelIds.map(uniqueId => {
                        // 查找对应的模型名称
                        for (const group of modelGroups) {
                            const model = group.models.find(m => m.id === uniqueId);
                            if (model) {
                                return model.name;
                            }
                        }
                        return uniqueId; // 如果找不到，使用原ID作为备用
                    });

                    const importRequest = {
                        serviceAccountJson: googleServiceAccountJson,
                        name: data.name,
                        supportModels: modelNames,
                        autoDisable: data.autoDisable || false,
                        modelMapping: data.modelMapping,
                        quota: data.quota ? parseFloat(data.quota) : undefined,
                        weight: data.weight ? parseFloat(data.weight) : 1.0,
                        windowSize: data.windowSize ? parseInt(data.windowSize) : 100,
                        epsilon: data.epsilon ? parseFloat(data.epsilon) : 0.05,
                        errThreshold: data.errThreshold ? parseFloat(data.errThreshold) : 0.5
                    };

                    await commonKeyApi.importGoogleVertexAI(importRequest);
                } else {
                    console.log("Creating generic key...");
                    await commonKeyApi.createKey(data);
                }

                toast.success("密钥创建成功");
            }
        },
        {
            manual: true,
            onSuccess: () => {
                onSuccess();
                handleClose();
            },
            onError: (error: any) => {
                toast.error(error.message || "密钥创建失败");
            }
        }
    );

    const handleNext = () => {
        // Google Vertex AI 需要先验证JSON文件
        if (keyType === "GoogleVertexAI") {
            if (!parsedServiceAccount) {
                toast.error("请先上传有效的服务账户JSON文件");
                return;
            }
            setActiveTab("models");
        } else {
            form.handleSubmit(() => {
                setActiveTab("models");
            })();
        }
    };

    const handleSubmit = (data: any) => {
        if (selectedModelIds.length === 0) {
            toast.error("请至少选择一个模型");
            return;
        }

        // Google Vertex AI 特殊处理
        if (keyType === "GoogleVertexAI") {
            if (!parsedServiceAccount || !googleServiceAccountJson) {
                toast.error("请先上传有效的服务账户JSON文件");
                return;
            }
            console.log("Creating Google Vertex AI key...", {
                hasServiceAccount: !!parsedServiceAccount,
                hasJsonString: !!googleServiceAccountJson,
                selectedModels: selectedModelIds.length
            });
            createKey(data);
            return;
        }

        // 将唯一ID转换为模型名称
        const modelNames = selectedModelIds.map(uniqueId => {
            // 查找对应的模型名称
            for (const group of modelGroups) {
                const model = group.models.find(m => m.id === uniqueId);
                if (model) {
                    return model.name;
                }
            }
            return uniqueId; // 如果找不到，使用原ID作为备用
        });

        const submitData: any = {
            ...data,
            type: keyType,
            supportModels: modelNames,
        };

        // 清理不需要的字段
        if (keyType !== "ClaudeCode") {
            delete submitData.refreshToken;
            delete submitData.clientId;
            delete submitData.email;
            delete submitData.expiresAt;
            delete submitData.accountType;
        }

        createKey(submitData);
    };

    const handleClose = () => {
        setOpen(false);
        onClose();
    };

    // 处理Google服务账户JSON文件
    const handleFileRead = useCallback((content: string) => {
        setGoogleServiceAccountJson(content);
        setParseError("");

        try {
            const json = JSON.parse(content) as GoogleServiceAccountJson;

            // 验证必要字段
            if (json.type !== "service_account") {
                throw new Error("JSON文件不是有效的服务账户格式（type字段必须为'service_account'）");
            }

            if (!json.project_id || !json.private_key || !json.client_email) {
                throw new Error("缺少必要字段：project_id、private_key 或 client_email");
            }

            setParsedServiceAccount(json);
            // 自动设置密钥名称
            form.setValue("name", `${json.project_id} - ${json.client_email}`);
        } catch (e) {
            setParseError(e instanceof Error ? e.message : "JSON解析失败");
            setParsedServiceAccount(null);
        }
    }, [form]);

    const [fileUploadState, fileUploadActions] = useFileUpload({
        accept: 'application/json,.json',
        maxSize: 1024 * 1024, // 1MB
        multiple: true, // 支持多文件上传
        onFilesAdded: (files) => {
            if (files.length > 0) {
                // 处理第一个文件来解析和设置表单
                const file = files[0].file;
                if (file instanceof File) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const content = e.target?.result as string;
                        handleFileRead(content);
                    };
                    reader.readAsText(file);
                }
            }
        }
    });

    const currentFile = fileUploadState.files[0]?.file instanceof File ? fileUploadState.files[0].file : null;

    const handleModelToggle = (modelId: string) => {
        setSelectedModelIds((prev) =>
            prev.includes(modelId)
                ? prev.filter((id) => id !== modelId)
                : [...prev, modelId]
        );
    };

    const handleVendorBulkToggle = (vendorId: string, checked: boolean) => {
        const vendor = modelGroups.find((g) => g.id === vendorId);
        if (vendor) {
            const vendorModelIds = vendor.models.map((m) => m.id);

            if (checked) {
                setSelectedModelIds((prev) => [
                    ...prev.filter((id) => !vendorModelIds.includes(id)),
                    ...vendorModelIds
                ]);
            } else {
                setSelectedModelIds((prev) => prev.filter((id) => !vendorModelIds.includes(id)));
            }
        }
    };

    const isVendorFullySelected = (vendor: ModelGroup) => {
        return vendor.models.every((model) => selectedModelIds.includes(model.id));
    };

    const isVendorPartiallySelected = (vendor: ModelGroup) => {
        return vendor.models.some((model) => selectedModelIds.includes(model.id)) && !isVendorFullySelected(vendor);
    };

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <div tabIndex={0} className="sr-only" />
            <DialogContent className="sm:max-w-[90%] h-[90%] flex flex-col" hideCloseButton aria-describedby={undefined}>
                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0">
                    <DialogTitle className="sr-only">创建新密钥</DialogTitle>
                    <Button
                        variant="ghost"
                        className="rounded-none border-r flex items-center gap-2 h-full bg-white focus-visible:ring-0 focus-visible:border-gray-200"
                        onClick={handleClose}
                    >
                        <X className="h-4 w-4" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline">
                                esc
                            </Badge>
                        </span>
                    </Button>

                    <div className="flex flex-1 h-15 bg-white">
                        <TabButton
                            active={activeTab === "details"}
                            onClick={() => setActiveTab("details")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "details" ? "progress-blue" : "completed"}
                                    size="sm"
                                />
                            }
                            label="基本信息"
                        />
                        <TabButton
                            active={activeTab === "models"}
                            onClick={() => setActiveTab("models")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "models" ? "progress-blue" : selectedModelIds.length > 0 ? "completed" : "pending"}
                                    size="sm"
                                />
                            }
                            label="绑定模型"
                        />
                        <TabButton
                            active={activeTab === "advanced"}
                            onClick={() => setActiveTab("advanced")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "advanced" ? "progress-blue" : "pending"}
                                    size="sm"
                                />
                            }
                            label="高级选项"
                        />
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-auto p-6">
                    {activeTab === "details" && (
                        <Form {...form}>
                            <form className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>密钥名称（可选）</FormLabel>
                                            <FormControl>
                                                <Input placeholder="例如：生产环境密钥" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="space-y-2">
                                    <Label>密钥类型</Label>
                                    <Select value={keyType} onValueChange={(value) => setKeyType(value as KeyChannel)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="ClaudeCode">Claude Code</SelectItem>
                                            <SelectItem value="Openai">OpenAI</SelectItem>
                                            <SelectItem value="Azure">Azure</SelectItem>
                                            <SelectItem value="Anthropic">Anthropic</SelectItem>
                                            <SelectItem value="Aws">AWS</SelectItem>
                                            <SelectItem value="GoogleAiStudio">Google AI Studio</SelectItem>
                                            <SelectItem value="GoogleVertexAI">Google Vertex AI</SelectItem>
                                            <SelectItem value="Agent">Agent</SelectItem>
                                            <SelectItem value="GoogleSearch">Google Search</SelectItem>
                                            <SelectItem value="BingSearch">Bing Search</SelectItem>
                                            <SelectItem value="Ark">Ark</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {keyType === "ClaudeCode" ? (
                                    <>
                                        <FormField
                                            control={form.control}
                                            name="refreshToken"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>刷新令牌（支持批量）</FormLabel>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="请输入刷新令牌，支持批量输入：每行一个，或使用 | 分隔字段（refreshToken|clientId|email|accountType|name）"
                                                            className="min-h-[80px]"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        批量格式：每行一个refreshToken，或使用 refreshToken|clientId|email|accountType|name 格式
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="clientId"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>客户端ID</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder="请输入客户端ID" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="email"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>邮箱（可选）</FormLabel>
                                                    <FormControl>
                                                        <Input type="email" placeholder="<EMAIL>" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="accountType"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>账户类型</FormLabel>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="选择账户类型" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value="PRO">专业版</SelectItem>
                                                            <SelectItem value="MAX_100">Max 100</SelectItem>
                                                            <SelectItem value="MAX_200">Max 200</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="accessToken"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>访问令牌（可选）</FormLabel>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="留空将自动刷新获取"
                                                            className="min-h-[80px]"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </>
                                ) : keyType === "GoogleVertexAI" ? (
                                    <>
                                        <div className="space-y-4">
                                            <Alert>
                                                <AlertDescription>
                                                    请上传从Google Cloud Console下载的服务账户JSON文件。该文件应包含project_id、private_key等必要信息。
                                                </AlertDescription>
                                            </Alert>

                                            <div
                                                onDragEnter={fileUploadActions.handleDragEnter}
                                                onDragLeave={fileUploadActions.handleDragLeave}
                                                onDragOver={fileUploadActions.handleDragOver}
                                                onDrop={fileUploadActions.handleDrop}
                                                onClick={fileUploadActions.openFileDialog}
                                                className={`
                                                    border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
                                                    transition-colors duration-200
                                                    ${fileUploadState.isDragging ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-gray-400'}
                                                    ${currentFile ? 'bg-green-50 border-green-300' : ''}
                                                `}
                                            >
                                                <input {...fileUploadActions.getInputProps()} />
                                                {currentFile ? (
                                                    <div className="space-y-2">
                                                        <FileJson className="h-8 w-8 mx-auto text-green-600" />
                                                        <p className="text-sm font-medium">{currentFile.name}</p>
                                                        <p className="text-xs text-gray-500">
                                                            {(currentFile.size / 1024).toFixed(2)} KB
                                                        </p>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                fileUploadActions.clearFiles();
                                                                setGoogleServiceAccountJson("");
                                                                setParsedServiceAccount(null);
                                                                setParseError("");
                                                            }}
                                                        >
                                                            清除文件
                                                        </Button>
                                                    </div>
                                                ) : (
                                                    <div className="space-y-2">
                                                        <Upload className="h-8 w-8 mx-auto text-gray-400" />
                                                        <p className="text-sm text-gray-600">
                                                            {fileUploadState.isDragging ? '释放以上传文件' : '拖拽文件到此处或点击选择（支持多文件）'}
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            仅支持JSON文件，最大1MB，支持同时上传多个文件进行批量创建
                                                        </p>
                                                    </div>
                                                )}
                                            </div>

                                            {(parseError || fileUploadState.errors.length > 0) && (
                                                <Alert variant="destructive">
                                                    <AlertDescription>
                                                        {parseError && <div>{parseError}</div>}
                                                        {fileUploadState.errors.map((error, index) => (
                                                            <div key={index}>{error}</div>
                                                        ))}
                                                    </AlertDescription>
                                                </Alert>
                                            )}

                                            {parsedServiceAccount && (
                                                <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                                                    <h3 className="font-medium text-sm">服务账户信息</h3>
                                                    <div className="space-y-1 text-sm">
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">项目ID:</span>
                                                            <span className="font-mono">{parsedServiceAccount.project_id}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">服务账户邮箱:</span>
                                                            <span className="font-mono text-xs">{parsedServiceAccount.client_email}</span>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <span className="text-gray-600">私钥ID:</span>
                                                            <span className="font-mono text-xs">{parsedServiceAccount.private_key_id.substring(0, 8)}...</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        <FormField
                                            control={form.control}
                                            name="accessToken"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>访问令牌（支持批量）</FormLabel>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="请输入访问令牌，支持批量输入：每行一个，或使用 | 分隔字段（accessToken|domain|name）"
                                                            className="min-h-[80px]"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        批量格式：每行一个accessToken，或使用 accessToken|domain|name 格式
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="domain"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>域名（可选）</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder="例如：api.openai.com" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="quota"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>配额金额（可选）</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            placeholder="例如：100.00"
                                                            {...field}
                                                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                                            value={field.value || ''}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </>
                                )}
                            </form>
                        </Form>
                    )}

                    {activeTab === "models" && (
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <Label className="text-base font-medium">选择模型</Label>
                                <Badge variant="outline">{selectedModelIds.length} 已选择</Badge>
                            </div>

                            {modelsLoading ? (
                                <div className="flex justify-center py-8">
                                    <div className="text-muted-foreground">加载模型列表...</div>
                                </div>
                            ) : (
                                <Tabs defaultValue={modelGroups[0]?.vendor} className="w-full">
                                    <TabsList className="grid w-full grid-cols-4">
                                        {modelGroups.map((vendor) => {
                                            const selectedCount = vendor.models.filter((m) => selectedModelIds.includes(m.id)).length;
                                            return (
                                                <TabsTrigger key={vendor.vendor} value={vendor.vendor} className="gap-2">
                                                    {vendor.vendor}
                                                    <Badge variant="outline" className="ml-1">
                                                        {selectedCount}/{vendor.models.length}
                                                    </Badge>
                                                </TabsTrigger>
                                            );
                                        })}
                                    </TabsList>

                                    {modelGroups.map((vendor) => {
                                        const isFullySelected = isVendorFullySelected(vendor);
                                        const isPartiallySelected = isVendorPartiallySelected(vendor);

                                        return (
                                            <TabsContent key={vendor.vendor} value={vendor.vendor} className="space-y-4 mt-4">
                                                <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                                    <div className="flex items-center space-x-3">
                                                        <Checkbox
                                                            checked={isFullySelected}
                                                            ref={(ref) => {
                                                                if (ref) {
                                                                    (ref as any).indeterminate = isPartiallySelected;
                                                                }
                                                            }}
                                                            onCheckedChange={(checked) => handleVendorBulkToggle(vendor.id, checked as boolean)}
                                                        />
                                                        <div>
                                                            <h3 className="font-medium">选择所有 {vendor.vendor} 模型</h3>
                                                            <p className="text-sm text-slate-500">{vendor.models.length} 个可用模型</p>
                                                        </div>
                                                    </div>
                                                    <Badge variant="secondary">
                                                        {vendor.models.filter((m) => selectedModelIds.includes(m.id)).length}/{vendor.models.length}
                                                    </Badge>
                                                </div>

                                                <div className="grid sm:grid-cols-6 grid-cols-2 gap-2">
                                                    {vendor.models.map((model) => (
                                                        <div
                                                            key={model.id}
                                                            className="flex items-center space-x-2 p-2 rounded hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors cursor-pointer"
                                                            onClick={() => handleModelToggle(model.id)}
                                                        >
                                                            <Checkbox
                                                                checked={selectedModelIds.includes(model.id)}
                                                                onCheckedChange={() => handleModelToggle(model.id)}
                                                                className="pointer-events-none"
                                                            />
                                                            <div className="flex-1 min-w-0">
                                                                <p className="font-medium text-sm truncate">{model.name}</p>
                                                            </div>
                                                            {selectedModelIds.includes(model.id) && (
                                                                <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </TabsContent>
                                        );
                                    })}
                                </Tabs>
                            )}
                        </div>
                    )}

                    {activeTab === "advanced" && (
                        <Form {...form}>
                            <form className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="autoDisable"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-base">
                                                    自动禁用
                                                </FormLabel>
                                                <div className="text-sm text-muted-foreground">
                                                    认证失败时自动禁用密钥
                                                </div>
                                            </div>
                                            <FormControl>
                                                <Checkbox
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="modelMapping"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>模型映射配置</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder={`{"gpt-4": "gpt-4-turbo", "claude-3": "claude-3-opus"}`}
                                                    className="min-h-[100px] font-mono text-sm"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <div className="text-xs text-muted-foreground">
                                                JSON格式，key为原始模型名，value为映射后的模型名
                                            </div>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* 调度器配置 */}
                                <div className="space-y-4 border-t pt-4">
                                    <div className="text-sm font-medium text-muted-foreground">
                                        智能调度器配置
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="weight"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>权重</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.1"
                                                            max="10.0"
                                                            step="0.1"
                                                            placeholder="1.0"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(1.0); // 创建时使用默认值
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? 1.0 : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        权重越高被选中概率越大 (0.1-10.0)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="windowSize"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>滑动窗口大小</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="10"
                                                            max="1000"
                                                            step="10"
                                                            placeholder="100"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(100); // 创建时使用默认值
                                                                } else {
                                                                    const numValue = parseInt(value);
                                                                    field.onChange(isNaN(numValue) ? 100 : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        统计错误率的样本数量 (10-1000)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="epsilon"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>探索率</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.01"
                                                            max="0.5"
                                                            step="0.01"
                                                            placeholder="0.05"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(0.05); // 创建时使用默认值
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? 0.05 : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        随机选择的概率 (0.01-0.5)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="errThreshold"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>错误阈值</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.1"
                                                            max="0.9"
                                                            step="0.1"
                                                            placeholder="0.5"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(0.5); // 创建时使用默认值
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? 0.5 : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        超过此错误率的密钥将被过滤 (0.1-0.9)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div>
                            </form>
                        </Form>
                    )}
                </div>

                <div className="border-t flex justify-end items-center p-2 gap-2 flex-shrink-0">
                    {activeTab === "details" ? (
                        <>
                            <CommonButton variant="outline" onClick={handleClose}>
                                取消
                            </CommonButton>
                            <CommonButton onClick={handleNext}>
                                下一步
                            </CommonButton>
                        </>
                    ) : activeTab === "models" ? (
                        <>
                            <CommonButton variant="outline" onClick={() => setActiveTab("details")}>
                                上一步
                            </CommonButton>
                            <CommonButton onClick={() => setActiveTab("advanced")}>
                                下一步
                            </CommonButton>
                        </>
                    ) : (
                        <>
                            <CommonButton variant="outline" onClick={() => setActiveTab("models")}>
                                上一步
                            </CommonButton>
                            <CommonButton
                                onClick={() => {
                                    console.log("Create button clicked, keyType:", keyType);
                                    if (keyType === "GoogleVertexAI") {
                                        // Google Vertex AI不需要表单验证，直接调用handleSubmit
                                        handleSubmit({});
                                    } else {
                                        // 其他类型需要表单验证
                                        form.handleSubmit(handleSubmit)();
                                    }
                                }}
                                loading={loading}
                            >
                                创建密钥
                            </CommonButton>
                        </>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export function TabButton({
    active,
    onClick,
    icon,
    label,
    disabled = false,
}: {
    active: boolean;
    onClick: () => void;
    icon: React.ReactNode;
    label: string;
    disabled?: boolean;
}) {
    return (
        <button
            className={`flex w-56 font-light items-center gap-2 px-4 py-2 relative border-r border-zinc-200 ${disabled
                ? "opacity-50 cursor-not-allowed"
                : active
                    ? "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px]"
                    : "bg-zinc-100"
                }`}
            onClick={disabled ? undefined : onClick}
            disabled={disabled}
        >
            {icon}
            <span className={`text-sm ${active ? "text-foreground" : "text-muted-foreground"}`}>{label}</span>
        </button>
    );
}

export default CreateKeyModal;