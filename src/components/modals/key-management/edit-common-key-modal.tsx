"use client"

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/new/dialog";
import CommonButton from "@/components/ui/button/new-button";
import { useRequest } from "ahooks";
import { commonKeyApi } from "@/api/key-management/common-key-api";
import { toast } from "sonner";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { type CommonTokenKey } from "@/api/key-management/common-key-model";
import { Badge } from "@/components/ui/badge";
import { X, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import StatusIcon from "@/components/icons/status-icon";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { SystemModelResponse } from "@/api/system-model/system-model-model";
import { systemModelApi } from "@/api/system-model/system-model-api";

interface EditKeyModalProps {
    keyData: CommonTokenKey;
    onClose: () => void;
    onSuccess?: () => void;
}

const formSchema = z.object({
    name: z.string().optional(),
    accessToken: z.string().optional(),
    refreshToken: z.string().optional(),
    expiresAt: z.string().optional(),
    clientId: z.string().optional(),
    email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
    accountType: z.enum(["PRO", "MAX_100", "MAX_200"]).optional(),
    status: z.enum(["ACTIVE", "INACTIVE", "AUTH_FAILED"]).optional(),
    domain: z.string().optional(),
    supportModels: z.array(z.string()).optional(),
    autoDisable: z.boolean().optional(),
    modelMapping: z.string().optional(),
    quota: z.number().optional(),
    // 调度器配置字段
    weight: z.number().min(0.1).max(10.0).optional(),
    windowSize: z.number().min(10).max(1000).optional(),
    epsilon: z.number().min(0.01).max(0.5).optional(),
    errThreshold: z.number().min(0.1).max(0.9).optional(),
    // Google Vertex AI 相关字段
    projectId: z.string().optional(),
    clientEmail: z.string().optional(),
    privateKeyId: z.string().optional()
});

type FormData = z.infer<typeof formSchema>;

interface ModelGroup {
    id: string;
    vendor: string;
    models: {
        id: string;
        name: string;
        logoUrl?: string;
    }[];
}

/**
 * 将后端系统模型数据转换为前端需要的格式并按厂商分组
 */
function transformAndGroupModels(systemModels: SystemModelResponse[]): ModelGroup[] {
    // 创建一个Set来跟踪已使用的唯一标识符，确保唯一性
    const usedIds = new Set<string>();

    // 按厂商分组
    const groupedByManufacturer = systemModels.reduce((acc, model, index) => {
        const manufacturer = model.manufacturerName || "未知厂商"
        if (!acc[manufacturer]) {
            acc[manufacturer] = []
        }

        const modelName = model.presentationModel || model.modelName || "未知模型";
        const manufacturerSlug = manufacturer.toLowerCase().replace(/\s+/g, '-');

        // 使用厂商名称+模型名称生成唯一标识符
        let uniqueId = `${manufacturerSlug}-${modelName.toLowerCase().replace(/\s+/g, '-')}`;

        // 如果仍然重复，添加数组索引
        if (usedIds.has(uniqueId)) {
            uniqueId = `${uniqueId}-${index}`;
        }

        // 如果还是重复，添加时间戳
        if (usedIds.has(uniqueId)) {
            uniqueId = `${uniqueId}-${Date.now()}`;
        }

        // 最后保险，添加随机数
        let counter = 1;
        let finalId = uniqueId;
        while (usedIds.has(finalId)) {
            finalId = `${uniqueId}-${Math.random().toString(36).substr(2, 9)}-${counter}`;
            counter++;
        }

        usedIds.add(finalId);

        // 添加调试日志
        if (process.env.NODE_ENV === 'development') {
            console.log(`Generated unique ID: ${finalId} for model: ${modelName}`);
        }

        const transformedModel = {
            id: finalId,
            name: modelName,
            logoUrl: model.logoUrl
        }

        acc[manufacturer].push(transformedModel)
        return acc
    }, {} as Record<string, any[]>)

    // 转换为 ModelGroup 数组
    return Object.entries(groupedByManufacturer).map(([manufacturer, models]) => ({
        id: manufacturer.toLowerCase().replace(/\s+/g, '-'),
        vendor: manufacturer,
        models: models
    }))
}

export const EditCommonKeyModal = ({ keyData, onClose, onSuccess }: EditKeyModalProps) => {
    const [open, setOpen] = useState(true);
    const [activeTab, setActiveTab] = useState<"details" | "models" | "advanced">("details");
    const [modelGroups, setModelGroups] = useState<ModelGroup[]>([]);
    const [selectedModelIds, setSelectedModelIds] = useState<string[]>([]);

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: keyData.name || "",
            accessToken: "", // 不显示原始token
            refreshToken: "", // 不显示原始token
            expiresAt: keyData.claudeCodeRefreshInfo?.expiresAt?.toString() || "",
            clientId: keyData.claudeCodeRefreshInfo?.clientId || "",
            email: keyData.claudeCodeRefreshInfo?.email || "",
            accountType: keyData.claudeCodeRefreshInfo?.accountType,
            status: keyData.status === 'DISABLED' ? 'INACTIVE' : keyData.status === 'AUTH_FAILED' ? 'AUTH_FAILED' : 'ACTIVE',
            domain: keyData.domain || "",
            supportModels: keyData.supportModels || [],
            autoDisable: keyData.autoDisable || false,
            modelMapping: keyData.modelMapping || "",
            quota: keyData.quota || undefined,
            // 调度器配置字段
            weight: keyData.weight || 1.0,
            windowSize: keyData.windowSize || 100,
            epsilon: keyData.epsilon || 0.05,
            errThreshold: keyData.errThreshold || 0.5,
            // Google Vertex AI 相关字段
            projectId: keyData.googleVertexAIRefreshInfo?.projectId || "",
            clientEmail: keyData.googleVertexAIRefreshInfo?.clientEmail || "",
            privateKeyId: keyData.googleVertexAIRefreshInfo?.privateKeyId || ""
        }
    });

    // 从后端获取模型数据
    const { loading: modelsLoading } = useRequest(
        systemModelApi.getDisplayModels,
        {
            onSuccess: (data: SystemModelResponse[]) => {
                const groupedModels = transformAndGroupModels(data)
                setModelGroups(groupedModels)

                // 将已存储的模型名称转换为对应的唯一ID
                if (keyData.supportModels && keyData.supportModels.length > 0) {
                    const selectedIds: string[] = [];
                    keyData.supportModels.forEach(modelName => {
                        // 在所有分组中查找匹配的模型名称
                        for (const group of groupedModels) {
                            const model = group.models.find(m => m.name === modelName);
                            if (model) {
                                selectedIds.push(model.id);
                                break; // 找到后跳出循环
                            }
                        }
                    });
                    setSelectedModelIds(selectedIds);
                }
            },
            onError: (error) => {
                toast.error(`获取模型列表失败: ${error.message}`)
            }
        }
    );

    const { run: updateKey, loading } = useRequest(
        async (data: any) => {
            if (keyData.type === "ClaudeCode") {
                await commonKeyApi.updateClaudeCodeKey({
                    id: keyData.id,
                    ...data
                });
            } else if (keyData.type === "GoogleVertexAI") {
                await commonKeyApi.updateGoogleVertexAIKey({
                    id: keyData.id,
                    ...data
                });
            } else {
                await commonKeyApi.updateKey({
                    id: keyData.id,
                    ...data
                });
            }
        },
        {
            manual: true,
            onSuccess: () => {
                toast.success("更新密钥成功");
                onSuccess?.();
                setOpen(false);
            },
            onError: (error: any) => {
                toast.error(`更新密钥失败: ${error.message}`);
            }
        }
    );

    const handleSubmit = (data: FormData) => {
        // 将唯一ID转换为模型名称
        const modelNames = selectedModelIds.map(uniqueId => {
            // 查找对应的模型名称
            for (const group of modelGroups) {
                const model = group.models.find(m => m.id === uniqueId);
                if (model) {
                    return model.name;
                }
            }
            return uniqueId; // 如果找不到，使用原ID作为备用
        });

        const submitData: any = {
            supportModels: modelNames
        };

        // 只提交有值的字段
        if (data.name) submitData.name = data.name;
        if (data.accessToken) submitData.accessToken = data.accessToken;
        if (data.status) {
            // 将前端状态值转换为后端枚举值
            if (data.status === 'INACTIVE') {
                submitData.status = 'DISABLED';
            } else {
                submitData.status = data.status;
            }
        }
        if (data.autoDisable !== undefined) submitData.autoDisable = data.autoDisable;
        if (data.modelMapping !== undefined) submitData.modelMapping = data.modelMapping;
        if (data.quota !== undefined) submitData.quota = data.quota;

        // 调度器配置字段 - 确保是有效的数字值
        if (data.weight !== undefined && !isNaN(data.weight)) submitData.weight = data.weight;
        if (data.windowSize !== undefined && !isNaN(data.windowSize)) submitData.windowSize = data.windowSize;
        if (data.epsilon !== undefined && !isNaN(data.epsilon)) submitData.epsilon = data.epsilon;
        if (data.errThreshold !== undefined && !isNaN(data.errThreshold)) submitData.errThreshold = data.errThreshold;

        if (keyData.type === "ClaudeCode") {
            // Claude Code 特有字段
            if (data.refreshToken) submitData.refreshToken = data.refreshToken;
            if (data.expiresAt) submitData.expiresAt = parseInt(data.expiresAt);
            if (data.clientId) submitData.clientId = data.clientId;
            if (data.email) submitData.email = data.email;
            if (data.accountType) submitData.accountType = data.accountType;
        } else if (keyData.type === "GoogleVertexAI") {
            // Google Vertex AI 特有字段 - 显示但不允许编辑
            // 这些字段通常不应该被更改，因为它们来自服务账户JSON
        } else {
            // 其他类型密钥
            if (data.domain) submitData.domain = data.domain;
        }

        updateKey(submitData);
    };

    const handleClose = () => {
        setOpen(false);
        onClose();
    };

    const handleModelToggle = (modelId: string) => {
        setSelectedModelIds((prev) =>
            prev.includes(modelId)
                ? prev.filter((id) => id !== modelId)
                : [...prev, modelId]
        );
    };

    const handleVendorBulkToggle = (vendorId: string, checked: boolean) => {
        const vendor = modelGroups.find((g) => g.id === vendorId);
        if (vendor) {
            const vendorModelIds = vendor.models.map((m) => m.id);

            if (checked) {
                setSelectedModelIds((prev) => [
                    ...prev.filter((id) => !vendorModelIds.includes(id)),
                    ...vendorModelIds
                ]);
            } else {
                setSelectedModelIds((prev) => prev.filter((id) => !vendorModelIds.includes(id)));
            }
        }
    };

    const isVendorFullySelected = (vendor: ModelGroup) => {
        return vendor.models.every((model) => selectedModelIds.includes(model.id));
    };

    const isVendorPartiallySelected = (vendor: ModelGroup) => {
        return vendor.models.some((model) => selectedModelIds.includes(model.id)) && !isVendorFullySelected(vendor);
    };

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <div tabIndex={0} className="sr-only" />
            <DialogContent className="sm:max-w-[90%] h-[90%] flex flex-col" hideCloseButton aria-describedby={undefined}>
                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0">
                    <DialogTitle className="sr-only">编辑密钥</DialogTitle>
                    <Button
                        variant="ghost"
                        className="rounded-none border-r flex items-center gap-2 h-full bg-white focus-visible:ring-0 focus-visible:border-gray-200"
                        onClick={handleClose}
                    >
                        <X className="h-4 w-4" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline">
                                esc
                            </Badge>
                        </span>
                    </Button>

                    <div className="flex flex-1 h-15 bg-white">
                        <TabButton
                            active={activeTab === "details"}
                            onClick={() => setActiveTab("details")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "details" ? "progress-blue" : "completed"}
                                    size="sm"
                                />
                            }
                            label="基本信息"
                        />
                        <TabButton
                            active={activeTab === "models"}
                            onClick={() => setActiveTab("models")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "models" ? "progress-blue" : "pending"}
                                    size="sm"
                                />
                            }
                            label="绑定模型"
                        />
                        <TabButton
                            active={activeTab === "advanced"}
                            onClick={() => setActiveTab("advanced")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "advanced" ? "progress-blue" : "pending"}
                                    size="sm"
                                />
                            }
                            label="高级选项"
                        />
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-auto p-6">
                    {activeTab === "details" && (
                        <Form {...form}>
                            <form className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>密钥名称</FormLabel>
                                            <FormControl>
                                                <Input placeholder="为密钥起一个名称" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="accessToken"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>访问令牌</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="输入新的访问令牌（留空保持不变）"
                                                    className="min-h-[80px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {keyData.type === "ClaudeCode" ? (
                                    <>
                                        <FormField
                                            control={form.control}
                                            name="refreshToken"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>刷新令牌</FormLabel>
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="输入新的刷新令牌（留空保持不变）"
                                                            className="min-h-[80px]"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="clientId"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>客户端ID</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder="请输入Claude客户端ID" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="email"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>邮箱地址</FormLabel>
                                                    <FormControl>
                                                        <Input placeholder="请输入关联的邮箱地址" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="accountType"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>账户类型</FormLabel>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="选择Claude账户类型" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value={'PRO'}>专业版</SelectItem>
                                                            <SelectItem value={'MAX_100'}>Max 100</SelectItem>
                                                            <SelectItem value={'MAX_200'}>Max 200</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </>
                                ) : keyData.type === "GoogleVertexAI" ? (
                                    <>
                                        <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                                            <h3 className="font-medium text-sm mb-3">Google Vertex AI 服务账户信息</h3>
                                            <div className="grid grid-cols-1 gap-4">
                                                <div className="space-y-2">
                                                    <Label className="text-sm font-medium">项目ID</Label>
                                                    <Input
                                                        value={keyData.googleVertexAIRefreshInfo?.projectId || ""}
                                                        disabled
                                                        className="bg-gray-100"
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label className="text-sm font-medium">服务账户邮箱</Label>
                                                    <Input
                                                        value={keyData.googleVertexAIRefreshInfo?.clientEmail || ""}
                                                        disabled
                                                        className="bg-gray-100"
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label className="text-sm font-medium">私钥ID</Label>
                                                    <Input
                                                        value={keyData.googleVertexAIRefreshInfo?.privateKeyId ?
                                                            keyData.googleVertexAIRefreshInfo.privateKeyId.substring(0, 16) + "..." : ""}
                                                        disabled
                                                        className="bg-gray-100"
                                                    />
                                                </div>
                                                {keyData.googleVertexAIRefreshInfo?.lastRefreshTime && (
                                                    <div className="space-y-2">
                                                        <Label className="text-sm font-medium">上次刷新时间</Label>
                                                        <Input
                                                            value={keyData.googleVertexAIRefreshInfo.lastRefreshTime}
                                                            disabled
                                                            className="bg-gray-100"
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                            <div className="text-xs text-gray-500 mt-2">
                                                注意：Google Vertex AI 服务账户信息无法直接修改，如需更改请重新导入新的服务账户JSON文件。
                                            </div>
                                        </div>
                                    </>
                                ) : (
                                    <FormField
                                        control={form.control}
                                        name="domain"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>域名</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="例如：api.openai.com" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                )}

                                <FormField
                                    control={form.control}
                                    name="quota"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>配额金额</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    placeholder="例如：100.00"
                                                    {...field}
                                                    onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                                                    value={field.value || ''}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="status"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>状态</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="选择密钥状态" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    <SelectItem value={'ACTIVE'}>活跃</SelectItem>
                                                    <SelectItem value={'INACTIVE'}>禁用</SelectItem>
                                                    <SelectItem value={'AUTH_FAILED'}>认证失败</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {keyData.type === "ClaudeCode" && (
                                    <FormField
                                        control={form.control}
                                        name="expiresAt"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>过期时间戳</FormLabel>
                                                <FormControl>
                                                    <Input
                                                        type="number"
                                                        placeholder="请输入过期时间戳（秒）"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                )}
                            </form>
                        </Form>
                    )}

                    {activeTab === "models" && (
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <Label className="text-base font-medium">选择模型</Label>
                                <Badge variant="outline">{selectedModelIds.length} 已选择</Badge>
                            </div>

                            {modelsLoading ? (
                                <div className="flex justify-center py-8">
                                    <div className="text-muted-foreground">加载模型列表...</div>
                                </div>
                            ) : (
                                <Tabs defaultValue={modelGroups[0]?.vendor} className="w-full">
                                    <TabsList className="grid w-full grid-cols-4">
                                        {modelGroups.map((vendor) => {
                                            const selectedCount = vendor.models.filter((m) => selectedModelIds.includes(m.id)).length;
                                            return (
                                                <TabsTrigger key={vendor.vendor} value={vendor.vendor} className="gap-2">
                                                    {vendor.vendor}
                                                    <Badge variant="outline" className="ml-1">
                                                        {selectedCount}/{vendor.models.length}
                                                    </Badge>
                                                </TabsTrigger>
                                            );
                                        })}
                                    </TabsList>

                                    {modelGroups.map((vendor) => {
                                        const isFullySelected = isVendorFullySelected(vendor);
                                        const isPartiallySelected = isVendorPartiallySelected(vendor);

                                        return (
                                            <TabsContent key={vendor.vendor} value={vendor.vendor} className="space-y-4 mt-4">
                                                <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                                    <div className="flex items-center space-x-3">
                                                        <Checkbox
                                                            checked={isFullySelected}
                                                            ref={(ref) => {
                                                                if (ref) {
                                                                    (ref as any).indeterminate = isPartiallySelected;
                                                                }
                                                            }}
                                                            onCheckedChange={(checked) => handleVendorBulkToggle(vendor.id, checked as boolean)}
                                                        />
                                                        <div>
                                                            <h3 className="font-medium">选择所有 {vendor.vendor} 模型</h3>
                                                            <p className="text-sm text-slate-500">{vendor.models.length} 个可用模型</p>
                                                        </div>
                                                    </div>
                                                    <Badge variant="secondary">
                                                        {vendor.models.filter((m) => selectedModelIds.includes(m.id)).length}/{vendor.models.length}
                                                    </Badge>
                                                </div>

                                                <div className="grid sm:grid-cols-6 grid-cols-2 gap-2">
                                                    {vendor.models.map((model) => (
                                                        <div
                                                            key={model.id}
                                                            className="flex items-center space-x-2 p-2 rounded hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors cursor-pointer"
                                                            onClick={() => handleModelToggle(model.id)}
                                                        >
                                                            <Checkbox
                                                                checked={selectedModelIds.includes(model.id)}
                                                                onCheckedChange={() => handleModelToggle(model.id)}
                                                                className="pointer-events-none"
                                                            />
                                                            <div className="flex-1 min-w-0">
                                                                <p className="font-medium text-sm truncate">{model.name}</p>
                                                            </div>
                                                            {selectedModelIds.includes(model.id) && (
                                                                <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </TabsContent>
                                        );
                                    })}
                                </Tabs>
                            )}
                        </div>
                    )}

                    {activeTab === "advanced" && (
                        <Form {...form}>
                            <form className="space-y-4">
                                <FormField
                                    control={form.control}
                                    name="autoDisable"
                                    render={({ field }) => (
                                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                            <div className="space-y-0.5">
                                                <FormLabel className="text-base">
                                                    自动禁用
                                                </FormLabel>
                                                <div className="text-sm text-muted-foreground">
                                                    认证失败时自动禁用密钥
                                                </div>
                                            </div>
                                            <FormControl>
                                                <Checkbox
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="modelMapping"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>模型映射配置</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder={`{"gpt-4": "gpt-4-turbo", "claude-3": "claude-3-opus"}`}
                                                    className="min-h-[100px] font-mono text-sm"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <div className="text-xs text-muted-foreground">
                                                JSON格式，key为原始模型名，value为映射后的模型名
                                            </div>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* 调度器配置 */}
                                <div className="space-y-4 border-t pt-4">
                                    <div className="text-sm font-medium text-muted-foreground">
                                        智能调度器配置
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="weight"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>权重</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.1"
                                                            max="10.0"
                                                            step="0.1"
                                                            placeholder="1.0"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(undefined);
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? undefined : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        权重越高被选中概率越大 (0.1-10.0)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="windowSize"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>滑动窗口大小</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="10"
                                                            max="1000"
                                                            step="10"
                                                            placeholder="100"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(undefined);
                                                                } else {
                                                                    const numValue = parseInt(value);
                                                                    field.onChange(isNaN(numValue) ? undefined : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        统计错误率的样本数量 (10-1000)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="epsilon"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>探索率</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.01"
                                                            max="0.5"
                                                            step="0.01"
                                                            placeholder="0.05"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(undefined);
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? undefined : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        随机选择的概率 (0.01-0.5)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        <FormField
                                            control={form.control}
                                            name="errThreshold"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>错误阈值</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0.1"
                                                            max="0.9"
                                                            step="0.1"
                                                            placeholder="0.5"
                                                            value={field.value ?? ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value === '') {
                                                                    field.onChange(undefined);
                                                                } else {
                                                                    const numValue = parseFloat(value);
                                                                    field.onChange(isNaN(numValue) ? undefined : numValue);
                                                                }
                                                            }}
                                                        />
                                                    </FormControl>
                                                    <div className="text-xs text-muted-foreground">
                                                        超过此错误率的密钥将被过滤 (0.1-0.9)
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </div>
                                </div>
                            </form>
                        </Form>
                    )}
                </div>

                <div className="border-t flex justify-end items-center p-2 gap-2 flex-shrink-0">
                    <CommonButton variant="outline" onClick={handleClose}>
                        取消
                    </CommonButton>
                    <CommonButton
                        onClick={() => form.handleSubmit(handleSubmit)()}
                        loading={loading}
                    >
                        更新密钥
                    </CommonButton>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export function TabButton({
    active,
    onClick,
    icon,
    label,
    disabled = false,
}: {
    active: boolean;
    onClick: () => void;
    icon: React.ReactNode;
    label: string;
    disabled?: boolean;
}) {
    return (
        <button
            className={`flex w-56 font-light items-center gap-2 px-4 py-2 relative border-r border-zinc-200 ${disabled
                ? "opacity-50 cursor-not-allowed"
                : active
                    ? "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px]"
                    : "bg-zinc-100"
                }`}
            onClick={disabled ? undefined : onClick}
            disabled={disabled}
        >
            {icon}
            <span className={`text-sm ${active ? "text-foreground" : "text-muted-foreground"}`}>{label}</span>
        </button>
    );
}