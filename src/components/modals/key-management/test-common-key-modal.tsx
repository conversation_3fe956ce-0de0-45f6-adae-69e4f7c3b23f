import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import CommonButton from "@/components/ui/button/new-button";
import { commonKeyApi } from "@/api/key-management/common-key-api";
import { toast } from "sonner";
import { type CommonTokenKey, type ApiTestResult } from "@/api/key-management/common-key-model";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { FlaskConical, Loader2, Eye } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface TestKeyModalProps {
    keyData: CommonTokenKey;
    onClose: () => void;
}

export const TestCommonKeyModal = ({ keyData, onClose }: TestKeyModalProps) => {
    const [open, setOpen] = useState(true);
    const [testResults, setTestResults] = useState<Map<string, ApiTestResult | null>>(new Map());
    const [testingModels, setTestingModels] = useState<Set<string>>(new Set());
    const [isBatchTesting, setIsBatchTesting] = useState(false);
    const [cancelBatchTesting, setCancelBatchTesting] = useState(false);

    // 清除测试状态的统一函数
    const clearTestingStatus = (model: string) => {
        setTestingModels(prev => {
            const newSet = new Set(prev);
            newSet.delete(model);
            return newSet;
        });
    };

    // 设置测试结果的统一函数
    const setTestResult = (model: string, result: ApiTestResult | null) => {
        setTestResults(prev => new Map(prev).set(model, result));
    };

    // 单个模型测试函数 - 返回Promise以支持并发
    const testSingleModel = async (model: string): Promise<void> => {
        // 如果已经在测试中，跳过
        if (testingModels.has(model)) {
            return;
        }

        try {
            // 设置测试状态
            setTestingModels(prev => new Set(prev).add(model));

            const result = await commonKeyApi.testKey(keyData.id!, keyData.type.toLowerCase(), model);

            // 设置结果
            setTestResult(model, result);

            // 显示结果提示
            if (result.statusCode === 200) {
                toast.success(`模型 ${model} 测试成功`);
            } else {
                toast.error(`模型 ${model} 测试失败: ${result.statusCode}`);
            }
        } catch (error: any) {
            // 设置失败结果
            setTestResult(model, {
                statusCode: 0,
                body: error.message || '请求失败'
            });
            toast.error(`模型 ${model} 测试失败: ${error.message}`);
        } finally {
            // 无论成功失败都清除loading状态
            clearTestingStatus(model);
        }
    };

    const handleTestModel = (model: string) => {
        // 使用新的测试函数替代原来的方式
        testSingleModel(model);
    };

    const handleTestAll = async () => {
        // 获取需要测试的模型（排除正在测试的）
        const modelsToTest = keyData.supportModels.filter(model => !testingModels.has(model));

        if (modelsToTest.length === 0) {
            toast.info('所有模型都在测试中或已完成测试');
            return;
        }

        setIsBatchTesting(true);
        setCancelBatchTesting(false);

        try {
            toast.info(`开始并发测试 ${modelsToTest.length} 个模型...`);

            // 设置并发限制，避免同时发起过多请求
            const CONCURRENT_LIMIT = 5;
            const results: PromiseSettledResult<void>[] = [];

            // 分批处理并发请求
            for (let i = 0; i < modelsToTest.length; i += CONCURRENT_LIMIT) {
                // 检查是否被取消
                if (cancelBatchTesting) {
                    toast.warning('批量测试已被取消');
                    break;
                }

                const batch = modelsToTest.slice(i, i + CONCURRENT_LIMIT);
                const batchPromises = batch.map(model => testSingleModel(model));
                const batchResults = await Promise.allSettled(batchPromises);
                results.push(...batchResults);

                // 如果还有更多批次，添加短暂延迟
                if (i + CONCURRENT_LIMIT < modelsToTest.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            // 只有在没有被取消的情况下才显示完成消息
            if (!cancelBatchTesting) {
                // 统计结果
                const completed = results.filter(result => result.status === 'fulfilled').length;
                const failed = results.filter(result => result.status === 'rejected').length;

                if (failed === 0) {
                    toast.success(`所有 ${completed} 个模型测试完成`);
                } else {
                    toast.warning(`测试完成：${completed} 个成功，${failed} 个异常`);
                }
            }

        } catch (error: any) {
            if (!cancelBatchTesting) {
                toast.error(`批量测试出现异常: ${error.message}`);
            }
        } finally {
            setIsBatchTesting(false);
            setCancelBatchTesting(false);
        }
    };

    // 重置所有测试状态
    const resetAllTestStates = () => {
        setTestingModels(new Set());
        setTestResults(new Map());
        setCancelBatchTesting(false);
        setIsBatchTesting(false);
        toast.info('已重置所有测试状态');
    };

    // 取消批量测试
    const cancelBatchTest = () => {
        setCancelBatchTesting(true);
        toast.info('正在取消批量测试...');
    };

    const handleClose = () => {
        setOpen(false);
        onClose();
    };


    const getStatusBadge = (model: string) => {
        if (testingModels.has(model)) {
            return <Badge variant="secondary" className="text-blue-600">测试中</Badge>;
        }

        const result = testResults.get(model);
        if (!result) {
            return <Badge variant="outline">未测试</Badge>;
        }

        if (result.statusCode === 200) {
            return <Badge variant="default" className="bg-green-100 text-green-700 hover:bg-green-100">成功</Badge>;
        } else {
            return <Badge variant="destructive">失败</Badge>;
        }
    };

    const truncateMessage = (message: string, maxLength: number = 50) => {
        if (!message) return '';
        return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
    };

    const isAnyTesting = testingModels.size > 0 || isBatchTesting;

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-screen-2xl h-[90vh] max-h-[90vh] overflow-hidden flex flex-col" aria-describedby={undefined}>
                <DialogHeader>
                    <DialogTitle>测试密钥</DialogTitle>
                    <DialogDescription>
                        对密钥支持的 {keyData.supportModels.length} 个模型进行连通性测试
                    </DialogDescription>
                </DialogHeader>

                <div className="flex-1 space-y-4 overflow-hidden">
                    <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">支持的模型 ({keyData.supportModels.length})</p>
                        <div className="flex items-center gap-2">
                            {(testingModels.size > 0 || testResults.size > 0) && !isBatchTesting && (
                                <CommonButton
                                    size="sm"
                                    variant="outline"
                                    onClick={resetAllTestStates}
                                    disabled={isAnyTesting}
                                >
                                    重置状态
                                </CommonButton>
                            )}
                            {isBatchTesting ? (
                                <CommonButton
                                    size="sm"
                                    variant="destructive"
                                    onClick={cancelBatchTest}
                                    disabled={cancelBatchTesting}
                                >
                                    {cancelBatchTesting ? '取消中...' : '取消测试'}
                                </CommonButton>
                            ) : null}
                            <CommonButton
                                size="xs"
                                onClick={handleTestAll}
                                disabled={isAnyTesting || keyData.supportModels.length === 0}
                            >
                                {isBatchTesting ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                        批量测试中 ({testingModels.size}/{keyData.supportModels.length})
                                    </>
                                ) : (
                                    <>
                                        <FlaskConical className="w-4 h-4 mr-1" />
                                        测试全部
                                    </>
                                )}
                            </CommonButton>
                        </div>
                    </div>

                    <div className="border rounded-lg overflow-hidden">
                        {keyData.supportModels.length === 0 ? (
                            <div className="p-8 text-center text-gray-500">
                                该密钥未绑定任何模型
                            </div>
                        ) : (
                            <div className="max-h-[calc(100vh-20rem)] overflow-auto">
                                <Table className="table-fixed w-full">
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-[300px]">模型名称</TableHead>
                                            <TableHead className="w-[100px]">状态码</TableHead>
                                            <TableHead className="w-[120px]">状态</TableHead>
                                            <TableHead className="w-auto min-w-[300px]">响应消息</TableHead>
                                            <TableHead className="w-[120px]">操作</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {keyData.supportModels.map((model) => {
                                            const result = testResults.get(model);
                                            const isTesting = testingModels.has(model);

                                            return (
                                                <TableRow key={model} className="hover:bg-gray-50">
                                                    <TableCell className="font-medium w-[200px] truncate">
                                                        <div className="truncate" title={model}>
                                                            {model}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="w-[100px]">
                                                        {result ? (
                                                            <span className={cn(
                                                                "font-mono text-sm",
                                                                result.statusCode === 200 ? "text-green-600" : "text-red-600"
                                                            )}>
                                                                {result.statusCode}
                                                            </span>
                                                        ) : (
                                                            <span className="text-gray-400">-</span>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="w-[120px]">
                                                        {getStatusBadge(model)}
                                                    </TableCell>
                                                    <TableCell className="min-w-[300px]">
                                                        {result?.body ? (
                                                            result.body.length > 50 ? (
                                                                <div className="flex items-center gap-2">
                                                                    <span className="text-sm text-gray-600 font-mono truncate flex-1">
                                                                        {truncateMessage(result.body)}
                                                                    </span>
                                                                    <Popover>
                                                                        <PopoverTrigger asChild>
                                                                            <CommonButton size="sm" variant="ghost" className="h-6 w-6 p-0 flex-shrink-0">
                                                                                <Eye className="h-3 w-3" />
                                                                            </CommonButton>
                                                                        </PopoverTrigger>
                                                                        <PopoverContent className="w-96 max-h-60 overflow-auto">
                                                                            <div>
                                                                                <p className="font-medium text-sm mb-2">完整响应消息</p>
                                                                                <pre className="text-xs text-gray-700 whitespace-pre-wrap break-words">
                                                                                    {result.body}
                                                                                </pre>
                                                                            </div>
                                                                        </PopoverContent>
                                                                    </Popover>
                                                                </div>
                                                            ) : (
                                                                <span className="text-sm text-gray-600 font-mono truncate block">
                                                                    {result.body}
                                                                </span>
                                                            )
                                                        ) : (
                                                            <span className="text-gray-400">-</span>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="w-[100px]">
                                                        <CommonButton
                                                            size="xs"
                                                            variant="outline"
                                                            onClick={() => handleTestModel(model)}
                                                            disabled={isTesting}
                                                            className="w-full"
                                                        >
                                                            {isTesting ? (
                                                                <>
                                                                    <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                                                                    测试中
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <FlaskConical className="w-3 h-3 mr-1" />
                                                                    测试
                                                                </>
                                                            )}
                                                        </CommonButton>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </div>
                </div>

                <DialogFooter>
                    <CommonButton variant="outline" onClick={handleClose}>
                        关闭
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};