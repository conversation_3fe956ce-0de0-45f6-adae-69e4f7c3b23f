import { useState } from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Dialog<PERSON>ooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useRequest } from "ahooks";
import { llmPriceApi } from "@/api/llm-price/llm-price-api";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { PRICE_STATUS_OPTIONS } from "@/api/llm-price/llm-price-model";
import type { LlmPriceUpdateRequest } from "@/api/llm-price/llm-price-model";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

const editLlmPriceSchema = z.object({
    statuses: z.coerce.number().optional(),
    // Text pricing
    textPrompt: z.string().optional(),
    textCompletion: z.string().optional(),
    textCachePrompt: z.string().optional(),
    textCachePromptWrite5m: z.string().optional(),
    textCachePromptWrite1h: z.string().optional(),
    // Audio pricing
    audioPrompt: z.string().optional(),
    audioCompletion: z.string().optional(),
    audioCachePrompt: z.string().optional(),
    // Image pricing
    imagePrompt: z.string().optional(),
    imageCompletion: z.string().optional(),
    imageCachePrompt: z.string().optional(),
    // Reasoning pricing
    reasoningCompletion: z.string().optional(),
});

interface EditLlmPriceModalProps {
    priceId: number;
    onClose: () => void;
    onSuccess?: () => void;
}

export const EditLlmPriceModal = ({ priceId, onClose, onSuccess }: EditLlmPriceModalProps) => {
    const [isOpen, setIsOpen] = useState(true);

    const form = useForm<z.infer<typeof editLlmPriceSchema>>({
        resolver: zodResolver(editLlmPriceSchema),
    });

    const { data: priceData, loading: loadingPrice } = useRequest(
        () => llmPriceApi.getById(priceId),
        {
            onSuccess: (data) => {
                form.reset({
                    statuses: data.statuses,
                    textPrompt: data.textPrompt || "",
                    textCompletion: data.textCompletion || "",
                    textCachePrompt: data.textCachePrompt || "",
                    textCachePromptWrite5m: data.textCachePromptWrite5m || "",
                    textCachePromptWrite1h: data.textCachePromptWrite1h || "",
                    audioPrompt: data.audioPrompt || "",
                    audioCompletion: data.audioCompletion || "",
                    audioCachePrompt: data.audioCachePrompt || "",
                    imagePrompt: data.imagePrompt || "",
                    imageCompletion: data.imageCompletion || "",
                    imageCachePrompt: data.imageCachePrompt || "",
                    reasoningCompletion: data.reasoningCompletion || "",
                });
            },
            onError: (error) => {
                toast.error("获取价格信息失败: " + error.message);
                handleClose();
            },
        }
    );

    const { runAsync: updatePrice, loading: updating } = useRequest(
        (data: LlmPriceUpdateRequest) => llmPriceApi.update(priceId, data),
        {
            manual: true,
            onSuccess: () => {
                toast.success("价格更新成功");
                onSuccess?.();
                handleClose();
            },
            onError: (error) => {
                toast.error("更新价格失败: " + error.message);
            },
        }
    );

    const handleClose = () => {
        setIsOpen(false);
        setTimeout(onClose, 200);
    };

    const onSubmit = async (values: z.infer<typeof editLlmPriceSchema>) => {
        const updateData: LlmPriceUpdateRequest = {};

        // Only include changed values
        Object.entries(values).forEach(([key, value]) => {
            if (value !== "" && value !== undefined) {
                updateData[key as keyof LlmPriceUpdateRequest] = value as any;
            }
        });

        await updatePrice(updateData);
    };

    if (loadingPrice) {
        return (
            <Dialog open={isOpen} onOpenChange={handleClose}>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>编辑LLM价格</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-32 w-full" />
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>编辑LLM价格</DialogTitle>
                    <DialogDescription>
                        修改系统模型的价格配置 - {priceData?.systemModelName}
                    </DialogDescription>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 gap-4">
                            <FormField
                                control={form.control}
                                name="statuses"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>状态</FormLabel>
                                        <Select
                                            value={field.value?.toString()}
                                            onValueChange={(value) => field.onChange(Number(value))}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="选择状态" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {PRICE_STATUS_OPTIONS.map((option) => (
                                                    <SelectItem key={option.value} value={option.value.toString()}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Tabs defaultValue="text" className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                                <TabsTrigger value="text">文本</TabsTrigger>
                                <TabsTrigger value="audio">音频</TabsTrigger>
                                <TabsTrigger value="image">图像</TabsTrigger>
                                <TabsTrigger value="reasoning">推理</TabsTrigger>
                            </TabsList>

                            <TabsContent value="text">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>文本价格配置</CardTitle>
                                        <CardDescription>
                                            配置文本输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="textPrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>文本输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>文本输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存文本的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePromptWrite5m"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>5分钟缓存写入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>5分钟缓存写入价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePromptWrite1h"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>1小时缓存写入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>1小时缓存写入价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="audio">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>音频价格配置</CardTitle>
                                        <CardDescription>
                                            配置音频输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="audioPrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>音频输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="audioCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>音频输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="audioCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存音频的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="image">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>图像价格配置</CardTitle>
                                        <CardDescription>
                                            配置图像输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="imagePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>图像输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="imageCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>图像输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="imageCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存图像的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="reasoning">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>推理价格配置</CardTitle>
                                        <CardDescription>
                                            配置高级推理能力的价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <FormField
                                            control={form.control}
                                            name="reasoningCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>推理输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>推理完成的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>

                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={handleClose}>
                                取消
                            </Button>
                            <Button type="submit" disabled={updating}>
                                更新
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};