import { useState, useMemo } from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useRequest } from "ahooks";
import { llmPriceApi } from "@/api/llm-price/llm-price-api";
import { systemModelApi } from "@/api/system-model/system-model-api";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { PRICE_STATUS_OPTIONS } from "@/api/llm-price/llm-price-model";
import type { LlmPriceCreateRequest } from "@/api/llm-price/llm-price-model";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const createLlmPriceSchema = z.object({
    systemModelId: z.string(),
    statuses: z.number().min(1).default(1),
    // Text pricing
    textPrompt: z.string().optional(),
    textCompletion: z.string().optional(),
    textCachePrompt: z.string().optional(),
    textCachePromptWrite5m: z.string().optional(),
    textCachePromptWrite1h: z.string().optional(),
    // Audio pricing
    audioPrompt: z.string().optional(),
    audioCompletion: z.string().optional(),
    audioCachePrompt: z.string().optional(),
    // Image pricing
    imagePrompt: z.string().optional(),
    imageCompletion: z.string().optional(),
    imageCachePrompt: z.string().optional(),
    // Reasoning pricing
    reasoningCompletion: z.string().optional(),
});

type CreateLlmPriceFormData = z.infer<typeof createLlmPriceSchema>;

interface CreateLlmPriceModalProps {
    onClose: () => void;
    onSuccess?: () => void;
}

export const CreateLlmPriceModal = ({ onClose, onSuccess }: CreateLlmPriceModalProps) => {
    const [isOpen, setIsOpen] = useState(true);

    const form = useForm({
        resolver: zodResolver(createLlmPriceSchema),
        defaultValues: {
            statuses: 1,
            // 不设置 systemModelId 的默认值，让用户必须选择
        },
    });

    const { data: systemModelsRaw = [] } = useRequest(() => systemModelApi.getAllModels(), {
        // 移除缓存以避免数据问题
        refreshDeps: [],
    });

    // 简化系统模型数据处理，只过滤有效数据
    const systemModels = useMemo(() => {
        return systemModelsRaw
            .filter(model => model && model.id && model.modelName)
            .map((model, index) => ({
                ...model,
                // 使用简单的唯一标识符
                displayName: `${model.modelName} (${model.manufacturerName || '未知厂商'})`,
                uniqueKey: `model_${model.id}_${index}` // 简单但唯一的 key
            }));
    }, [systemModelsRaw]);

    const { runAsync: createPrice, loading } = useRequest(llmPriceApi.create, {
        manual: true,
        onSuccess: () => {
            toast.success("价格创建成功");
            onSuccess?.();
            handleClose();
        },
        onError: (error) => {
            toast.error("创建价格失败: " + error.message);
        },
    });

    const handleClose = () => {
        setIsOpen(false);
        setTimeout(onClose, 200);
    };

    const onSubmit = async (values: CreateLlmPriceFormData) => {
        await createPrice(values as LlmPriceCreateRequest);
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[80%] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>新增LLM价格</DialogTitle>
                    <DialogDescription>
                        为系统模型配置不同模态的价格信息
                    </DialogDescription>
                </DialogHeader>

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="systemModelId"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>系统模型</FormLabel>
                                        <Select
                                            value={field.value ? String(field.value) : ""}
                                            onValueChange={(value) => {
                                                if (value && value !== "") {
                                                    field.onChange(value);
                                                } else {
                                                    field.onChange(undefined);
                                                }
                                            }}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="请选择系统模型" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {systemModels.length === 0 ? (
                                                    <div className="px-2 py-1 text-sm text-muted-foreground">
                                                        暂无可用模型
                                                    </div>
                                                ) : (
                                                    systemModels.map((model) => (
                                                        <SelectItem
                                                            key={`system-model-${model.id}`}
                                                            value={String(model.id)}
                                                        >
                                                            {model.displayName}
                                                        </SelectItem>
                                                    ))
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="statuses"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>状态</FormLabel>
                                        <Select
                                            value={field.value ? String(field.value) : ""}
                                            onValueChange={(value) => {
                                                if (value && value !== "") {
                                                    field.onChange(parseInt(value, 10));
                                                } else {
                                                    field.onChange(undefined);
                                                }
                                            }}
                                        >
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="请选择状态" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {PRICE_STATUS_OPTIONS.map((option) => (
                                                    <SelectItem key={`price-status-${option.value}`} value={String(option.value)}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Tabs defaultValue="text" className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                                <TabsTrigger value="text">文本</TabsTrigger>
                                <TabsTrigger value="audio">音频</TabsTrigger>
                                <TabsTrigger value="image">图像</TabsTrigger>
                                <TabsTrigger value="reasoning">推理</TabsTrigger>
                            </TabsList>

                            <TabsContent value="text">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>文本价格配置</CardTitle>
                                        <CardDescription>
                                            配置文本输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="textPrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>文本输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>文本输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存文本的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePromptWrite5m"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>5分钟缓存写入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>5分钟缓存写入价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="textCachePromptWrite1h"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>1小时缓存写入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>1小时缓存写入价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="audio">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>音频价格配置</CardTitle>
                                        <CardDescription>
                                            配置音频输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="audioPrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>音频输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="audioCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>音频输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="audioCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存音频的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="image">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>图像价格配置</CardTitle>
                                        <CardDescription>
                                            配置图像输入输出及缓存价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField
                                            control={form.control}
                                            name="imagePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输入价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>图像输入的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="imageCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>图像输出的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                        <FormField
                                            control={form.control}
                                            name="imageCachePrompt"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>缓存价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>缓存图像的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            <TabsContent value="reasoning">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>推理价格配置</CardTitle>
                                        <CardDescription>
                                            配置高级推理能力的价格（单位：美元/百万token）
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <FormField
                                            control={form.control}
                                            name="reasoningCompletion"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>推理输出价格</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="0.000000" />
                                                    </FormControl>
                                                    <FormDescription>推理完成的价格</FormDescription>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>

                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={handleClose}>
                                取消
                            </Button>
                            <Button type="submit" disabled={loading}>
                                创建
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};