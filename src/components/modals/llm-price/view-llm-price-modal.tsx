import { useState } from "react";
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { useRequest } from "ahooks";
import { llmPriceApi } from "@/api/llm-price/llm-price-api";
import { toast } from "sonner";
import { PRICE_STATUS_OPTIONS } from "@/api/llm-price/llm-price-model";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import dayjs from "dayjs";

interface ViewLlmPriceModalProps {
    priceId: number;
    onClose: () => void;
}

interface PriceItemProps {
    label: string;
    value: string | undefined | null;
    unit?: string;
}

const PriceItem = ({ label, value, unit = "美元/百万token" }: PriceItemProps) => {
    return (
        <div className="flex justify-between items-center py-2 border-b last:border-0">
            <span className="text-sm text-gray-600">{label}</span>
            <span className="font-mono text-sm">
                {value ? (
                    <>
                        ${value}
                        {unit && <span className="text-xs text-gray-500 ml-1">({unit})</span>}
                    </>
                ) : (
                    <span className="text-gray-400">未设置</span>
                )}
            </span>
        </div>
    );
};

export const ViewLlmPriceModal = ({ priceId, onClose }: ViewLlmPriceModalProps) => {
    const [isOpen, setIsOpen] = useState(true);

    const { data: priceData, loading } = useRequest(
        () => llmPriceApi.getById(priceId),
        {
            onError: (error) => {
                toast.error("获取价格信息失败: " + error.message);
                handleClose();
            },
        }
    );

    const handleClose = () => {
        setIsOpen(false);
        setTimeout(onClose, 200);
    };

    if (loading) {
        return (
            <Dialog open={isOpen} onOpenChange={handleClose}>
                <DialogContent className="max-w-3xl">
                    <DialogHeader>
                        <DialogTitle>LLM价格详情</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-32 w-full" />
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    const statusOption = PRICE_STATUS_OPTIONS.find((option) => option.value === priceData?.statuses);

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>LLM价格详情</DialogTitle>
                    <DialogDescription>
                        查看系统模型的价格配置信息
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                    {/* 基本信息 */}
                    <Card>
                        <CardHeader>
                            <CardTitle>基本信息</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-600">系统模型</label>
                                    <p className="mt-1 text-sm font-medium">
                                        {priceData?.systemModelName || "未知模型"}
                                        {priceData?.manufacturerName && (
                                            <span className="text-gray-500 ml-2">({priceData.manufacturerName})</span>
                                        )}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">状态</label>
                                    <p className="mt-1">
                                        <Badge
                                            variant={
                                                priceData?.statuses === 1
                                                    ? "default"
                                                    : priceData?.statuses === 0
                                                        ? "secondary"
                                                        : "destructive"
                                            }
                                        >
                                            {statusOption?.label || `状态${priceData?.statuses}`}
                                        </Badge>
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">创建时间</label>
                                    <p className="mt-1 text-sm">
                                        {priceData?.createTime
                                            ? dayjs(priceData.createTime).format("YYYY-MM-DD HH:mm:ss")
                                            : "-"}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* 价格详情 */}
                    <Tabs defaultValue="text" className="w-full">
                        <TabsList className="grid w-full grid-cols-4">
                            <TabsTrigger value="text">文本</TabsTrigger>
                            <TabsTrigger value="audio">音频</TabsTrigger>
                            <TabsTrigger value="image">图像</TabsTrigger>
                            <TabsTrigger value="reasoning">推理</TabsTrigger>
                        </TabsList>

                        <TabsContent value="text">
                            <Card>
                                <CardHeader>
                                    <CardTitle>文本价格</CardTitle>
                                    <CardDescription>文本模态的价格配置</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <PriceItem label="输入价格" value={priceData?.textPrompt} />
                                    <PriceItem label="输出价格" value={priceData?.textCompletion} />
                                    <PriceItem label="缓存价格" value={priceData?.textCachePrompt} />
                                    <PriceItem label="5分钟缓存写入" value={priceData?.textCachePromptWrite5m} />
                                    <PriceItem label="1小时缓存写入" value={priceData?.textCachePromptWrite1h} />
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="audio">
                            <Card>
                                <CardHeader>
                                    <CardTitle>音频价格</CardTitle>
                                    <CardDescription>音频模态的价格配置</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <PriceItem label="输入价格" value={priceData?.audioPrompt} />
                                    <PriceItem label="输出价格" value={priceData?.audioCompletion} />
                                    <PriceItem label="缓存价格" value={priceData?.audioCachePrompt} />
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="image">
                            <Card>
                                <CardHeader>
                                    <CardTitle>图像价格</CardTitle>
                                    <CardDescription>图像模态的价格配置</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <PriceItem label="输入价格" value={priceData?.imagePrompt} />
                                    <PriceItem label="输出价格" value={priceData?.imageCompletion} />
                                    <PriceItem label="缓存价格" value={priceData?.imageCachePrompt} />
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="reasoning">
                            <Card>
                                <CardHeader>
                                    <CardTitle>推理价格</CardTitle>
                                    <CardDescription>高级推理能力的价格配置</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <PriceItem label="推理输出价格" value={priceData?.reasoningCompletion} />
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </DialogContent>
        </Dialog>
    );
};