import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import { InfoIcon, Loader2, Mail, Shield } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { organizationState } from "@/state/user-state";
import { OrganizationBindApi } from "@/api/organization-bind/organization-bind-model";
import CommonButton from "@/components/ui/button/new-button";

interface AddTeamMemberModalProps {
    onSuccess: () => void;
}

const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
};

export function AddTeamMemberModal({ onSuccess }: AddTeamMemberModalProps) {
    const [userId, setUserId] = useState('');
    const [isOpen, setIsOpen] = useState(true);
    const [authority, setAuthority] = useState('1');
    const [emailError, setEmailError] = useState<string | null>(null);
    const organization = useAtomValue(organizationState);

    const { runAsync: addMember, loading } = useRequest(OrganizationBindApi.persistOrganizationBind, {
        manual: true,
        onSuccess: () => {
            toast.success('成员添加成功');
            onSuccess();
            setIsOpen(false);
        },
        onError: (e) => {
            toast.error('添加成员失败: ' + e.message);
        },
    });

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const email = e.target.value;
        setUserId(email);
        if (email && !validateEmail(email)) {
            setEmailError('请输入有效的邮箱地址');
        } else {
            setEmailError(null);
        }
    };

    const handleSubmit = () => {
        if (!userId) {
            setEmailError('请输入邮箱地址');
            return;
        }

        if (!validateEmail(userId)) {
            setEmailError('请输入有效的邮箱地址');
            return;
        }

        if (!authority || !organization?.organizationId) {
            toast.error('请填写所有必填项');
            return;
        }

        addMember(organization.organizationId, parseInt(authority), userId);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader className="pb-4">
                    <DialogTitle className="text-xl font-semibold">
                        <div className="flex items-center gap-3">
                            <div>
                                <h3 className="font-semibold text-gray-900 dark:text-gray-100">添加团队成员</h3>
                                <p className="text-sm font-normal text-gray-600 dark:text-gray-400 mt-1">
                                    邀请新成员加入您的团队
                                </p>
                            </div>
                        </div>
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* 邮箱输入区域 */}
                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-500" />
                            <label htmlFor="userId" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                邮箱地址
                            </label>
                        </div>
                        <div className="relative">
                            <Input
                                id="userId"
                                type="email"
                                value={userId}
                                onChange={handleEmailChange}
                                className={cn(
                                    "w-full pl-4 pr-4 py-3 text-sm border-gray-200 dark:border-gray-700",
                                    "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                                    "placeholder:text-gray-400 dark:placeholder:text-gray-500",
                                    emailError && "border-red-300 focus:ring-red-500 focus:border-red-500"
                                )}
                                placeholder="请输入成员邮箱地址，如：<EMAIL>"
                            />
                        </div>
                        {emailError && (
                            <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
                                <InfoIcon className="h-4 w-4" />
                                <span>{emailError}</span>
                            </div>
                        )}
                    </div>

                    {/* 权限选择区域 */}
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <Shield className="h-4 w-4 text-gray-500" />
                                <label htmlFor="authority" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    权限级别
                                </label>
                            </div>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <InfoIcon className='h-4 w-4 text-gray-400 hover:text-gray-600 cursor-help' />
                                    </TooltipTrigger>
                                    <TooltipContent side="left" className="max-w-xs">
                                        <div className="space-y-2 text-xs">
                                            <div><strong>所有者</strong>：拥有完整管理权限，可以修改团队成员和权限设置</div>
                                            <div><strong>成员</strong>：具有基础使用权限，无法修改团队设置</div>
                                        </div>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>

                        <Select value={authority} onValueChange={setAuthority}>
                            <SelectTrigger className="w-full h-11 border-gray-200 dark:border-gray-700">
                                <SelectValue placeholder="请选择权限级别" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="1" className="py-3">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                                        <div>
                                            <div className="font-medium">所有者</div>
                                            <div className="text-xs text-gray-500">完整管理权限</div>
                                        </div>
                                    </div>
                                </SelectItem>
                                <SelectItem value="0" className="py-3">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                        <div>
                                            <div className="font-medium">成员</div>
                                            <div className="text-xs text-gray-500">基础使用权限</div>
                                        </div>
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* 提示信息 */}
                    <div className="rounded-lg bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 p-4">
                        <div className="flex gap-3">
                            <InfoIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-blue-800 dark:text-blue-200">
                                <p className="font-medium mb-1">邀请说明</p>
                                <p className="text-blue-700 dark:text-blue-300">
                                    系统将向该邮箱发送邀请邮件，成员需要点击邮件中的链接完成加入。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <DialogFooter className="pt-6 gap-3 sm:gap-2">
                    <CommonButton
                        size="sm"
                        variant="outline"
                        onClick={() => setIsOpen(false)}
                        className='min-w-20'
                        disabled={loading}
                    >
                        取消
                    </CommonButton>
                    <CommonButton
                        size="sm"
                        onClick={handleSubmit}
                        disabled={loading || !!emailError || !userId.trim()}
                        className='min-w-24'
                    >
                        {loading ? (
                            <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>发送邀请中...</span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                <span>发送邀请</span>
                            </div>
                        )}
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}