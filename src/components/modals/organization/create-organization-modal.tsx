"use client"

import type React from "react"

import { useState } from "react"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/new/dialog"
import { Badge } from "@/components/ui/badge"
import StatusIcon from "@/components/icons/status-icon"
import CommonButton from "@/components/ui/button/new-button"
import { Input } from "@/components/ui/input"
import { organizationApi } from "@/api/organization/organization-api"
import { toast } from "sonner"
import { useRequest } from "ahooks"

export function CreateOrganizationModal() {
    const [open, setOpen] = useState(true)
    const [organizationName, setOrganizationName] = useState<string>("")
    const [activeTab, setActiveTab] = useState<"type" | "details" | "campaign">("type")

    const { runAsync: createOrganization, loading: createOrganizationLoading } = useRequest(organizationApi.createOrganization, {
        manual: true,
        onSuccess: () => {
            toast.success("创建组织成功");
            setOpen(false)
        },
        onError: (error) => {
            toast.error("创建组织失败: " + error.message)
        }
    })

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <div tabIndex={0} className="sr-only" />
            <DialogContent className="sm:max-w-[90%] h-[90%] flex flex-col" hideCloseButton aria-describedby={undefined}>
                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0">
                    <DialogTitle className="sr-only">创建组织</DialogTitle>
                    <Button
                        variant="ghost"
                        className="rounded-none border-r flex items-center gap-2 h-full bg-white focus-visible:ring-0 focus-visible:border-gray-200" onClick={() => setOpen(false)}
                    >
                        <X className="h-4 w-4" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline">
                                esc
                            </Badge>
                        </span>
                    </Button>

                    {/* sub  TabButton  same hover style*/}
                    <div className="flex flex-1 h-15 bg-white">
                        <TabButton
                            active={activeTab === "type"}
                            onClick={() => setActiveTab("type")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "type" ? "progress-blue" : "completed"}
                                    size="sm"
                                />
                            }
                            label="创建组织"
                        />
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-auto flex flex-col items-center justify-center">
                    {activeTab === "type" && (
                        <div className="space-y-4">
                            <h2 className="text-normal font-medium">组织名</h2>
                            <Input
                                placeholder="请输入组织名"
                                value={organizationName}
                                onChange={(e) => setOrganizationName(e.target.value)}
                            />
                        </div>
                    )}
                </div>

                <div className="border-t flex justify-end items-center p-2 gap-2 flex-shrink-0">
                    <CommonButton variant="outline" onClick={() => setOpen(false)}>
                        取消
                    </CommonButton>
                    <CommonButton
                        onClick={() => {
                            if (organizationName) {
                                createOrganization(organizationName)
                            } else {
                                toast.error("请输入组织名")
                            }
                        }}
                    >
                        {createOrganizationLoading ? "创建中..." : "创建"}
                    </CommonButton>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export function TabButton({
    active,
    onClick,
    icon,
    label,
}: {
    active: boolean
    onClick: () => void
    icon: React.ReactNode
    label: string
}) {
    return (
        <button
            className={`flex w-56 font-light items-center gap-2 px-4 py-2 relative border-r border-zinc-200  ${active ? "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px]  " : "bg-zinc-100"
                }`}
            onClick={onClick}
        >
            {icon}
            <span className={` text-sm ${active ? "text-foreground" : "text-muted-foreground"}`}>{label}</span>
        </button>
    )
}
