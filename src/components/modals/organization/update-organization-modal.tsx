import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTit<PERSON> } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import CommonButton from "@/components/ui/button/new-button"
import { useRequest } from "ahooks"
import { organizationApi } from "@/api/organization/organization-api"
import { toast } from "sonner"
import { useSetAtom } from "jotai"
import { refreshTableAtom } from "@/state/table-state"

interface UpdateOrganizationModalProps {
    organizationId: string
    organizationName: string
}

export const UpdateOrganizationModal = ({ organizationId, organizationName }: UpdateOrganizationModalProps) => {
    const [isOpen, setIsOpen] = useState(true)
    const [newOrganizationName, setNewOrganizationName] = useState<string>(organizationName)

    const refresh = useSetAtom(refreshTableAtom)

    const { runAsync: updateOrganization, loading: updateOrganizationLoading } = useRequest(organizationApi.updateOrganizationInfo, {
        manual: true,
        onSuccess: () => {
            toast.success("组织信息更新成功")
            setIsOpen(false)
            refresh(p => p + 1)
        },
        onError: (error) => {
            toast.error("更新组织信息失败: " + error.message)
        }
    })

    const handleSubmit = () => {
        if (!newOrganizationName.trim()) {
            toast.error("请输入组织名称")
            return
        }

        if (newOrganizationName.trim() === organizationName) {
            toast.info("组织名称未发生变化")
            setIsOpen(false)
            return
        }

        updateOrganization(organizationId, newOrganizationName.trim())
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                <DialogHeader>
                    <DialogTitle>编辑组织信息</DialogTitle>
                </DialogHeader>

                <div className="space-y-4 py-4">
                    <div className="space-y-2">
                        <label htmlFor="organizationName" className="text-sm font-medium text-gray-700">
                            组织名称
                        </label>
                        <Input
                            id="organizationName"
                            value={newOrganizationName}
                            onChange={(e) => setNewOrganizationName(e.target.value)}
                            placeholder="请输入组织名称"
                            className="w-full"
                        />
                    </div>

                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700">
                            组织ID
                        </label>
                        <div className="p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                            {organizationId}
                        </div>
                    </div>
                </div>

                <DialogFooter>
                    <CommonButton variant="outline" onClick={() => setIsOpen(false)}>
                        取消
                    </CommonButton>
                    <CommonButton
                        onClick={handleSubmit}
                        loading={updateOrganizationLoading}
                        disabled={!newOrganizationName.trim() || updateOrganizationLoading}
                    >
                        {updateOrganizationLoading ? "更新中..." : "确认更新"}
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
} 