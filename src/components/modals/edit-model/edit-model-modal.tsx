"use client"

import { useState, useRef } from "react"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/new/dialog"
import { Badge } from "@/components/ui/badge"
import CommonButton from "@/components/ui/button/new-button"
import ListEditor from "@/components/table/edit-table/edit-table"
import type { ListEditorHandle } from "@/components/table/edit-table/edit-table"
import { modelApi } from "@/api/model/model-api"
import { useAtomValue, useSetAtom } from "jotai"
import { organizationState } from "@/state/user-state"
import { toast } from "sonner"
import { useRequest } from "ahooks"
import { refreshTableAtom } from "@/state/table-state"

interface EditModelModalProps {
    selectedItems: any[]
}

export function EditModelModal({ selectedItems }: EditModelModalProps) {

    const [open, setOpen] = useState(true)

    const refresh = useSetAtom(refreshTableAtom)

    const organization = useAtomValue(organizationState)

    const listEditorRef = useRef<ListEditorHandle>(null);


    const { runAsync: updateModel, loading: updateModelLoading } = useRequest(modelApi.updateModel, {
        manual: true,
        onSuccess: () => {
            toast.success("保存成功")
            setOpen(false)
            refresh(p => p + 1)
        },
        onError: (error) => {
            toast.error("保存失败:" + (error as Error).message)
        }
    })

    const handleActualSave = async () => {
        if (listEditorRef.current) {
            const currentTableData = listEditorRef.current.getLatestData();
            updateModel(currentTableData.map((item: any) => ({
                systemModelId: item.id,
                organizationId: organization?.organizationId!!,
                rpm: item.rpm,
                tpm: item.tpm
            })))
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[90%] h-[90%] flex flex-col gap-0 bg-white" hideCloseButton aria-describedby={undefined} onEscapeKeyDown={(e) => {
                e.preventDefault()
            }}>
                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0 bg-white">
                    <DialogTitle className="sr-only">编辑模型</DialogTitle>
                    <Button
                        variant="ghost"
                        className="rounded-none flex items-center gap-2 h-full "
                        onClick={() => setOpen(false)}
                    >
                        <X className="h-4 w-4 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline" className="bg-[#F4F4F5]">
                                esc
                            </Badge>
                        </span>
                    </Button>
                </DialogHeader>
                <div className="flex-1 overflow-y-auto pt-0 mt-0">
                    <ListEditor
                        ref={listEditorRef}
                        initialItems={selectedItems}
                        columns={[
                            {
                                id: "manufacturer",
                                header: "厂商",
                                editable: false,
                                type: "text"
                            },
                            {
                                id: "modelName",
                                header: "模型名字",
                                editable: false,
                                type: "text"
                            },
                            {
                                id: "rpm",
                                header: "每分钟请求数",
                                editable: true,
                                type: "number"
                            },
                            {
                                id: "tpm",
                                header: "每分钟Token数",
                                editable: true,
                                type: "number"
                            },
                        ]}
                    />
                </div>

                <div className="border-t flex justify-end items-center p-2 gap-2 flex-shrink-0">
                    <CommonButton variant="outline" onClick={() => setOpen(false)}>
                        取消
                    </CommonButton>
                    <CommonButton
                        onClick={handleActualSave}
                        loading={updateModelLoading}
                    >
                        {updateModelLoading ? "保存中..." : "保存"}
                    </CommonButton>
                </div>
            </DialogContent>
        </Dialog>
    )
}