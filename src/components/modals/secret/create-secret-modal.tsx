import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import CommonButton from "@/components/ui/button/new-button"
import { useRequest } from "ahooks"
import { secretApi } from "@/api/secret/secret-api"
import { toast } from "sonner"
import { useAtomValue } from "jotai"
import { organizationState } from "@/state/user-state"
import { Copy, Eye, EyeOff, AlertTriangle, Download } from "lucide-react"
import { cn } from "@/lib/utils"
import * as clipboard from "clipboard-polyfill"
import type { OrganizationSecret } from "@/api/secret/secret-model"
import dayjs from "dayjs"

interface CreateSecretModalProps {
    onClose: () => void
    onSuccess?: () => void
}

export const CreateSecretModal = ({ onClose, onSuccess }: CreateSecretModalProps) => {
    const [copied, setCopied] = useState(false)
    const [isOpen, setIsOpen] = useState(true)
    const [showSecret, setShowSecret] = useState(false)
    const [step, setStep] = useState<'create' | 'display'>('create')
    const [createdSecret, setCreatedSecret] = useState<OrganizationSecret | null>(null)

    const organization = useAtomValue(organizationState)

    const { runAsync: createSecret, loading: createSecretLoading } = useRequest(secretApi.createSecret, {
        manual: true,
        onSuccess: (data) => {
            setCreatedSecret(data)
            setStep('display')
            toast.success("密钥创建成功")
        },
        onError: (error) => {
            toast.error("创建密钥失败: " + error.message)
        }
    })

    const handleCopySecret = async () => {
        if (createdSecret?.secretKey) {
            try {
                await clipboard.writeText(createdSecret.secretKey)
                setCopied(true)
                toast.success("密钥已复制到剪贴板")
                setTimeout(() => setCopied(false), 2000)
            } catch (error) {
                toast.error("复制失败")
            }
        }
    }

    const handleDownloadSecret = () => {
        if (createdSecret?.secretKey) {
            const element = document.createElement('a')
            const file = new Blob([`密钥: ${createdSecret.secretKey}\n创建时间: ${new Date(Number(createdSecret.createTime)).toLocaleString()}\n组织ID: ${createdSecret.organizationId}`], { type: 'text/plain' })
            element.href = URL.createObjectURL(file)
            element.download = `secret-${createdSecret.id}.txt`
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
            toast.success("密钥文件已下载")
        }
    }

    const handleClose = () => {
        setIsOpen(false)
        onClose()
    }

    const handleComplete = () => {
        handleClose()
        onSuccess?.()
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="w-full max-w-md sm:max-w-md" aria-describedby={undefined}>
                {step === 'create' ? (
                    <>
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                新建密钥
                            </DialogTitle>
                            <DialogDescription className="text-left space-y-2">
                                <p>即将为您创建一个新的API密钥，请注意：</p>
                                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                                    <li>密钥创建后将请复制</li>
                                    <li>请务必妥善保管，不要泄露给他人</li>
                                    <li>如果丢失，需要重新创建新密钥</li>
                                </ul>
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <CommonButton variant="outline" onClick={handleClose}>
                                取消
                            </CommonButton>
                            <CommonButton
                                loading={createSecretLoading}
                                onClick={async () => {
                                    await createSecret(organization?.organizationId!!)
                                }}
                            >
                                {createSecretLoading ? "创建中..." : "确认创建"}
                            </CommonButton>
                        </DialogFooter>
                    </>
                ) : (
                    <>
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                密钥创建成功
                            </DialogTitle>
                            <DialogDescription className="text-left">
                                您的API密钥已创建完成，请立即复制并妥善保管。关闭此窗口后将无法再次查看。
                            </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-4">
                            {/* 密钥信息 */}
                            {/* <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">密钥ID</label>
                                <div className="p-2 bg-gray-50 rounded-md text-sm font-mono text-gray-600">
                                    {createdSecret?.id}
                                </div>
                            </div> */}

                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">API密钥</label>
                                <div className="relative">
                                    <div className="p-3 bg-gray-50 border-2 border-dashed border-gray-200 rounded-md">
                                        <div className="flex items-start gap-2">
                                            <div className="flex-1 font-mono text-sm break-all overflow-hidden">
                                                {showSecret
                                                    ? createdSecret?.secretKey
                                                    : '••••••••••••••••••••••••••••••••'
                                                }
                                            </div>
                                            <div className="flex gap-1 flex-shrink-0">
                                                <button
                                                    onClick={() => setShowSecret(!showSecret)}
                                                    className="p-1 hover:bg-gray-200 rounded transition-colors"
                                                    title={showSecret ? "隐藏密钥" : "显示密钥"}
                                                >
                                                    {showSecret ? (
                                                        <EyeOff className="w-4 h-4 text-gray-500" />
                                                    ) : (
                                                        <Eye className="w-4 h-4 text-gray-500" />
                                                    )}
                                                </button>
                                                <button
                                                    onClick={handleCopySecret}
                                                    className={cn(
                                                        "p-1 rounded transition-colors",
                                                        copied
                                                            ? "bg-green-100 text-green-600"
                                                            : "hover:bg-gray-200 text-gray-500"
                                                    )}
                                                    title="复制密钥"
                                                >
                                                    <Copy className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">创建时间</label>
                                <div className="p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                                    {createdSecret?.createTime ? dayjs(Number(createdSecret.createTime)).format('YYYY-MM-DD HH:mm:ss') : ''}
                                </div>
                            </div>
                        </div>

                        {/* 安全提示 */}
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                            <div className="flex gap-2">
                                <AlertTriangle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-amber-800">
                                    <p className="font-medium">安全提示</p>
                                    <p className="mt-1">请立即复制并保存此密钥。建议将密钥保存在安全的密码管理器中。</p>
                                </div>
                            </div>
                        </div>

                        <DialogFooter className="flex-col sm:flex-row gap-2">
                            <CommonButton
                                variant="outline"
                                onClick={handleDownloadSecret}
                                className="w-full sm:w-auto"
                            >
                                <Download className="w-4 h-4" />
                                下载密钥文件
                            </CommonButton>
                            <CommonButton
                                onClick={handleComplete}
                                className="w-full sm:w-auto"
                            >
                                我已保存，完成
                            </CommonButton>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    )
}