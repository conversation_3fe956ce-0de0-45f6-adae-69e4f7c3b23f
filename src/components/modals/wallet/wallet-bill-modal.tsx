import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useState } from "react";
import CommonButton from "@/components/ui/button/new-button";
import { WalletApi } from "@/api/wallet/wallet-api";
import { useExcelDownload } from "@/hooks/use-export-file";
import { organizationState } from "@/state/user-state";
import { useAtomValue } from "jotai";
import { toast } from "sonner";
import { DatePickerWithRange } from "@/components/ui/date/date-rage-picker";
import React from "react";
import type { DateRange } from "react-day-picker";

export default function WalletBillModal() {

    const [open, setOpen] = useState(true);

    const organization = useAtomValue(organizationState);

    const [date, setDate] = React.useState<DateRange>(() => {
        const now = new Date();
        const lastMonth = new Date();
        lastMonth.setMonth(now.getMonth() - 1);
        return {
            from: lastMonth,
            to: now,
        };
    })

    const { downloadExcel, isLoading: loading } = useExcelDownload(WalletApi.exportBill);

    return <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
            <DialogHeader>
                <DialogTitle>导出账单</DialogTitle>
                <DialogDescription>选择导出账单的月份</DialogDescription>
            </DialogHeader>
            <div>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <DatePickerWithRange date={date} onSelect={setDate} />
                    </div>
                </div>
            </div>

            <DialogFooter>
                <CommonButton variant="outline" onClick={() => setOpen(false)}>取消</CommonButton>
                <CommonButton onClick={() => {
                    toast.promise(async () => {
                        // 使用yyyy-MM-dd格式
                        const formatDate = (date: Date) => {
                            const year = date.getUTCFullYear();
                            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
                            const day = String(date.getUTCDate()).padStart(2, '0');
                            return `${year}-${month}-${day}`;
                        };

                        const startDateStr = formatDate(date.from!!);

                        // 结束日期增加一天
                        const endDate = new Date(date.to!!);
                        endDate.setDate(endDate.getDate() + 1);
                        const endDateStr = formatDate(endDate);
                        await downloadExcel(organization?.organizationId, startDateStr, endDateStr)
                        setOpen(false)
                    },
                        {
                            loading: "导出中...",
                            success: "导出成功",
                            error: "导出失败"
                        }
                    )
                }} disabled={loading}>
                    {loading ? <div className="flex items-center gap-2"> 导出中...</div> : "导出"}
                </CommonButton>
            </DialogFooter>
        </DialogContent>
    </Dialog >
}