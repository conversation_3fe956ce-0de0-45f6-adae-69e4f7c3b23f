"use client"

import { useState } from "react"
import { X, History } from "lucide-react"
import { useRequest } from "ahooks"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/new/dialog"
import { Badge } from "@/components/ui/badge"
import CommonButton from "@/components/ui/button/new-button"
import { Loading } from "@/components/ui/loading"
import { UserWalletApi } from "@/api/user-wallet/user-wallet-api"
import dayjs from "dayjs"
import { cn, usageDiv } from "@/lib/utils"

export function UserWalletPreviewModal() {
    const [open, setOpen] = useState(true)

    // 获取钱包余额
    const { data: balance, loading: balanceLoading, error: balanceError } = useRequest(
        UserWalletApi.getBalance,
        {
            ready: open,
        }
    )

    // 获取充值记录
    const { data: records, loading: recordsLoading, error: recordsError } = useRequest(
        UserWalletApi.changeRecords,
        {
            ready: open,
        }
    )

    // 格式化金额显示
    const formatAmount = (amount: string | number) => {
        const num = typeof amount === 'string' ? parseFloat(amount) : amount
        const amount2 = num / usageDiv
        return isNaN(amount2) ? '0.00' : amount2.toFixed(2)
    }

    // 获取交易类型文本
    const getTransactionTypeText = (type: number) => {
        switch (type) {
            case 0:
                return { text: "赠送", color: "text-green-600", bg: "bg-green-50" }
            case 1:
                return { text: "充值", color: "text-blue-600", bg: "bg-blue-50" }
            case 2:
                return { text: "退款", color: "text-orange-600", bg: "bg-orange-50" }
            case 3:
                return { text: "划转", color: "text-yellow-600", bg: "bg-yellow-50" }
            default:
                return { text: "未知", color: "text-gray-600", bg: "bg-gray-50" }
        }
    }

    // 格式化日期
    const formatDate = (dateString: string) => {
        return dayjs(parseInt(dateString)).format('YYYY-MM-DD HH:mm:ss')
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-[90%] h-[90%] flex flex-col" hideCloseButton aria-describedby={undefined}>
                {/* 添加一个不可见的可聚焦元素来接收默认焦点 */}
                <div tabIndex={0} className="sr-only" />

                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0">
                    <DialogTitle className="sr-only">用户钱包</DialogTitle>
                    <Button
                        variant="ghost"
                        className="rounded-none border-r flex items-center gap-2 h-full bg-white focus-visible:ring-0 focus-visible:border-none"
                        onClick={() => setOpen(false)}
                    >
                        <X className="h-4 w-4" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline">
                                esc
                            </Badge>
                        </span>
                    </Button>

                    {/* 标题 */}
                    <div className="flex flex-1 h-15 bg-white items-center px-4">
                        <h2 className="text-zinc-600 text-sm ">用户钱包</h2>
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-auto p-6">
                    <div className="space-y-6">
                        {balanceLoading ? (
                            <div className="flex items-center justify-center h-32">
                                <Loading />
                            </div>
                        ) : balanceError ? (
                            <div className="flex items-center justify-center h-32 text-red-500">
                                加载余额失败: {balanceError.message}
                            </div>
                        ) : (
                            <div className="border border-gray-200 rounded-lg p-6 bg-white">
                                <div className="flex items-center justify-between mb-4">
                                    <h3 className="text-lg font-medium text-gray-900">当前余额</h3>
                                </div>
                                <div className="text-3xl font-bold text-black">
                                    ${balance?.toLocaleString() || 0}
                                </div>
                                <p className="text-sm text-gray-500 mt-2">
                                    可用于模型调用和其他服务
                                </p>
                            </div>
                        )}

                        {/* 充值记录 */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium text-gray-900">充值记录</h3>
                                <span className="text-sm text-gray-500">
                                    共 {records?.length || 0} 条记录
                                </span>
                            </div>

                            {recordsLoading ? (
                                <div className="flex items-center justify-center h-64">
                                    <Loading />
                                </div>
                            ) : recordsError ? (
                                <div className="flex items-center justify-center h-64 text-red-500">
                                    加载充值记录失败: {recordsError.message}
                                </div>
                            ) : records && records.length > 0 ? (
                                <div className="max-h-[400px] overflow-y-auto custom-scrollbar pr-1 space-y-3 border border-gray-100 rounded-lg p-2">
                                    {records.map((record) => {
                                        const typeInfo = getTransactionTypeText(record.amountOfMoneyType)
                                        return (
                                            <div key={record.id} className="border border-gray-200 rounded-lg p-4 bg-white hover:bg-gray-50 transition-colors">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-4">
                                                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${typeInfo.bg} ${typeInfo.color}`}>
                                                            {typeInfo.text}
                                                        </div>
                                                        <div>
                                                            <div className="font-medium text-black">
                                                                ${formatAmount(record.amountOfMoney)}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                {formatDate(record.createTime)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="text-right space-y-1">
                                                        <div className="text-sm text-gray-600">
                                                            <div>
                                                                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                                                                    <span
                                                                        className={cn("size-1.5 rounded-full ", record.statuses == 1 ? "bg-green-500" : "bg-red-500")}
                                                                        aria-hidden="true"
                                                                    ></span>
                                                                    {record.statuses === 1 ? "成功" : "失败"}
                                                                </Badge>
                                                            </div>
                                                        </div>
                                                        <div className="text-xs text-gray-400">
                                                            ID: {record.id.slice(0, 8)}...
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center h-64 text-gray-400">
                                    <History className="h-8 w-8 mb-4 opacity-50" />
                                    <p className="text text-gray-600">暂无充值记录</p>
                                    <p className="text-sm text-gray-500">您还没有进行过充值操作</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="border-t flex justify-end items-center p-4 gap-2 flex-shrink-0">
                    <CommonButton variant="outline" onClick={() => setOpen(false)}>
                        关闭
                    </CommonButton>
                </div>
            </DialogContent>
        </Dialog>
    )
}
