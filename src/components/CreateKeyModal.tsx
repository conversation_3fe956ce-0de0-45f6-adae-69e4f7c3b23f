import React, { useState } from 'react';
import { Modal, Form, Input, Select, Switch, Button, message } from 'antd';
import KeySchedulerConfigForm from './KeySchedulerConfigForm';
import { commonKeyApi } from '../api/key-management/common-key-api';
import type { CreateCommonTokenKeyRequest } from '../api/key-management/common-key-model';

const { Option } = Select;
const { TextArea } = Input;

interface CreateKeyModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const CreateKeyModal: React.FC<CreateKeyModalProps> = ({
  visible,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [schedulerConfig, setSchedulerConfig] = useState({
    weight: 1.0,
    windowSize: 100,
    epsilon: 0.05,
    errThreshold: 0.5
  });

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const request: CreateCommonTokenKeyRequest = {
        ...values,
        ...schedulerConfig,
        supportModels: values.supportModels || []
      };

      await commonKeyApi.createKey(request);
      message.success('密钥创建成功');
      form.resetFields();
      setSchedulerConfig({
        weight: 1.0,
        windowSize: 100,
        epsilon: 0.05,
        errThreshold: 0.5
      });
      onSuccess();
    } catch (error: any) {
      message.error(error.message || '创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSchedulerConfig({
      weight: 1.0,
      windowSize: 100,
      epsilon: 0.05,
      errThreshold: 0.5
    });
    onCancel();
  };

  return (
    <Modal
      title="创建密钥"
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          创建
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: 'Openai',
          autoDisable: false
        }}
      >
        <Form.Item
          label="密钥名称"
          name="name"
          rules={[{ required: true, message: '请输入密钥名称' }]}
        >
          <Input placeholder="请输入密钥名称" />
        </Form.Item>

        <Form.Item
          label="访问令牌"
          name="accessToken"
          rules={[{ required: true, message: '请输入访问令牌' }]}
        >
          <TextArea
            rows={3}
            placeholder="请输入访问令牌"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        <Form.Item
          label="渠道类型"
          name="type"
          rules={[{ required: true, message: '请选择渠道类型' }]}
        >
          <Select placeholder="请选择渠道类型">
            <Option value="Openai">OpenAI</Option>
            <Option value="ClaudeCode">Claude Code</Option>
            <Option value="Azure">Azure</Option>
            <Option value="Agent">Agent</Option>
            <Option value="Aws">AWS</Option>
            <Option value="Anthropic">Anthropic</Option>
            <Option value="GoogleAiStudio">Google AI Studio</Option>
            <Option value="GoogleVertexAI">Google Vertex AI</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="域名"
          name="domain"
        >
          <Input placeholder="请输入API域名（可选）" />
        </Form.Item>

        <Form.Item
          label="支持的模型"
          name="supportModels"
          rules={[{ required: true, message: '请至少选择一个支持的模型' }]}
        >
          <Select
            mode="tags"
            placeholder="请输入或选择支持的模型"
            tokenSeparators={[',']}
          >
            <Option value="gpt-4o">gpt-4o</Option>
            <Option value="gpt-4o-mini">gpt-4o-mini</Option>
            <Option value="claude-3-5-sonnet-20241022">claude-3-5-sonnet-20241022</Option>
            <Option value="claude-3-5-haiku-20241022">claude-3-5-haiku-20241022</Option>
            <Option value="gemini-1.5-pro">gemini-1.5-pro</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="自动禁用"
          name="autoDisable"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          label="模型映射"
          name="modelMapping"
        >
          <TextArea
            rows={2}
            placeholder='JSON格式的模型映射，例如: {"gpt-4": "gpt-4o"}'
          />
        </Form.Item>

        {/* 调度器配置组件 */}
        <KeySchedulerConfigForm
          initialValues={schedulerConfig}
          onChange={setSchedulerConfig}
        />
      </Form>
    </Modal>
  );
};

export default CreateKeyModal;
