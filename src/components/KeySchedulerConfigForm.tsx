import React, { useState } from 'react';
import { Form, InputNumber, Collapse, Tooltip, Space } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { Panel } = Collapse;

interface KeySchedulerConfigFormProps {
  initialValues?: {
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
    minSampleSize?: number;
  };
  onChange?: (values: any) => void;
}

const KeySchedulerConfigForm: React.FC<KeySchedulerConfigFormProps> = ({
  initialValues = {},
  onChange
}) => {
  const [form] = Form.useForm();
  const [, setIsAdvancedOpen] = useState(false);

  const handleValuesChange = (_changedValues: any, allValues: any) => {
    onChange?.(allValues);
  };

  const defaultValues = {
    weight: 1.0,
    windowSize: 100,
    epsilon: 0.05,
    errThreshold: 0.5,
    minSampleSize: 5,
    ...initialValues
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={defaultValues}
      onValuesChange={handleValuesChange}
    >
      <Collapse 
        ghost
        onChange={(keys) => setIsAdvancedOpen(keys.includes('advanced'))}
      >
        <Panel 
          header="高级选项 - 密钥调度器配置" 
          key="advanced"
          extra={
            <Tooltip title="配置密钥在智能调度系统中的行为参数">
              <QuestionCircleOutlined />
            </Tooltip>
          }
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Form.Item
              label={
                <Space>
                  权重 (Weight)
                  <Tooltip title="密钥的权重值，权重越高被选中的概率越大。范围：0.1-10.0">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="weight"
              rules={[
                { required: true, message: '请输入权重值' },
                { type: 'number', min: 0.1, max: 10.0, message: '权重值必须在 0.1 到 10.0 之间' }
              ]}
            >
              <InputNumber
                min={0.1}
                max={10.0}
                step={0.1}
                precision={1}
                style={{ width: '100%' }}
                placeholder="1.0"
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  滑动窗口大小 (Window Size)
                  <Tooltip title="用于统计错误率的滑动窗口大小，窗口越大统计越稳定但响应越慢。范围：10-1000">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="windowSize"
              rules={[
                { required: true, message: '请输入滑动窗口大小' },
                { type: 'number', min: 10, max: 1000, message: '滑动窗口大小必须在 10 到 1000 之间' }
              ]}
            >
              <InputNumber
                min={10}
                max={1000}
                step={10}
                style={{ width: '100%' }}
                placeholder="100"
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  探索率 (Epsilon)
                  <Tooltip title="ε-Greedy算法的探索率，控制随机选择的概率。值越大探索性越强。范围：0.01-0.5">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="epsilon"
              rules={[
                { required: true, message: '请输入探索率' },
                { type: 'number', min: 0.01, max: 0.5, message: '探索率必须在 0.01 到 0.5 之间' }
              ]}
            >
              <InputNumber
                min={0.01}
                max={0.5}
                step={0.01}
                precision={3}
                style={{ width: '100%' }}
                placeholder="0.05"
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  错误阈值 (Error Threshold)
                  <Tooltip title="错误率阈值，超过此值的密钥将被暂时过滤。范围：0.1-0.9">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="errThreshold"
              rules={[
                { required: true, message: '请输入错误阈值' },
                { type: 'number', min: 0.1, max: 0.9, message: '错误阈值必须在 0.1 到 0.9 之间' }
              ]}
            >
              <InputNumber
                min={0.1}
                max={0.9}
                step={0.1}
                precision={2}
                style={{ width: '100%' }}
                placeholder="0.5"
              />
            </Form.Item>

            <Form.Item
              label={
                <Space>
                  最小样本数 (Min Sample Size)
                  <Tooltip title="最小样本数阈值，样本数低于此值时不应用错误率过滤，给新密钥试错机会。范围：1-50">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="minSampleSize"
              rules={[
                { required: true, message: '请输入最小样本数' },
                { type: 'number', min: 1, max: 50, message: '最小样本数必须在 1 到 50 之间' }
              ]}
            >
              <InputNumber
                min={1}
                max={50}
                step={1}
                style={{ width: '100%' }}
                placeholder="5"
              />
            </Form.Item>

            <div style={{ 
              background: '#f6f8fa', 
              padding: '12px', 
              borderRadius: '6px',
              fontSize: '12px',
              color: '#666'
            }}>
              <strong>参数说明：</strong>
              <ul style={{ margin: '8px 0 0 0', paddingLeft: '16px' }}>
                <li><strong>权重</strong>：影响密钥被选中的概率，权重高的密钥更容易被选择</li>
                <li><strong>滑动窗口</strong>：统计错误率的样本数量，影响错误率计算的稳定性</li>
                <li><strong>探索率</strong>：随机选择密钥的概率，平衡探索和利用</li>
                <li><strong>错误阈值</strong>：错误率超过此值的密钥将被临时过滤</li>
                <li><strong>最小样本数</strong>：样本数低于此值时不应用错误率过滤，避免新密钥首次失败就被永久过滤</li>
              </ul>
            </div>
          </Space>
        </Panel>
      </Collapse>
    </Form>
  );
};

export default KeySchedulerConfigForm;
