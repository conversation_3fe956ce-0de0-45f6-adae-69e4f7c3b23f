
import HomePage from "@/app/dash/home/<USER>";
import ModelPage from "@/app/dash/model/page";
import OrganizationPage from "@/app/dash/organization/page";
import SecretPage from "@/app/dash/secret/page";
import TeamPage from "@/app/dash/team/page";
import WalletPage from "@/app/dash/wallet/page";
import KeyManagementPage from "@/app/dash/key-management/page";
import KeyMonitoringPage from "@/app/dash/key-monitoring/page";
import LlmPricePage from "@/app/dash/llm-price/page";
import LoginPage from "@/app/login/login";
import MainLayout from "@/components/layout/main-layout";
import { createBrowserRouter, Navigate } from "react-router";
export const router = createBrowserRouter([
    {
        path: "/",
        Component: MainLayout,
        children: [
            { index: true, element: <Navigate to="/dashboard/home" replace /> },
            {
                path: "dashboard",
                children: [
                    { path: "home", Component: HomePage },
                    { path: "model", Component: ModelPage },
                    { path: "organization", Component: OrganizationPage },
                    { path: "secret", Component: SecretPage },
                    { path: "team", Component: TeamPage },
                    { path: "wallet", Component: WalletPage },
                    { path: "key-management", Component: KeyManagementPage },
                    { path: "key-monitoring", Component: KeyMonitoringPage },
                    { path: "llm-price", Component: LlmPricePage },
                ]
            },
        ],
    },
    { path: "/login", Component: LoginPage },
]);
