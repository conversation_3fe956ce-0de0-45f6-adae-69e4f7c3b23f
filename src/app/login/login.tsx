import { email<PERSON>pi } from "@/api/email/email-api";
import { user<PERSON>pi } from "@/api/user/user-api";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot } from "@/components/ui/input-otp";
import { Label } from "@/components/ui/label";
import { userTokenKey } from "@/lib/apiClient";
import { cn } from "@/lib/utils";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { Check, Info, X } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";
import { toast } from "sonner";

type Step = 'login' | 'register' | 'verify' | 'setup-password' | 'forget-password' | 'forget-verify' | 'reset-password';

interface PasswordStrength {
    hasMinLength: boolean;
    hasUpperCase: boolean;
    hasLowerCase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
}

const LoginPage = () => {
    const navigate = useNavigate();
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [step, setStep] = useState<Step>('login');
    const [otp, setOtp] = useState("");
    const [errors, setErrors] = useState<{
        email?: string;
        password?: string;
        otp?: string;
        general?: string;
    }>({});

    const passwordStrength = useMemo<PasswordStrength>(() => {
        return {
            hasMinLength: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumber: /[0-9]/.test(password),
            hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        };
    }, [password]);

    const isPasswordValid = useMemo(() => {
        return Object.values(passwordStrength).every(Boolean);
    }, [passwordStrength]);

    const [searchParams] = useSearchParams();
    const signup = searchParams.get('signup');
    const optCode = searchParams.get('opt-code');
    const emailParam = searchParams.get('email');

    useEffect(() => {
        if (signup) {
            setStep('register');
        }
    }, [signup]);

    useEffect(() => {
        if (optCode && emailParam) {
            setStep('verify');
            setOtp(optCode);
            setEmail(emailParam);
        }
    }, [optCode, emailParam]);

    const setToken = (token: string) => {
        localStorage.setItem(userTokenKey, token);
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setErrors({});

        if (step === 'login') {
            if (!email && !password) {
                setErrors({
                    email: "请输入邮箱",
                    password: "请输入密码"
                });
                setIsLoading(false);
                return;
            }

            if (!email) {
                setErrors({ email: "请输入邮箱" });
                setIsLoading(false);
                return;
            }

            if (!password) {
                setErrors({ password: "请输入密码" });
                setIsLoading(false);
                return;
            }

            try {
                const response = await userApi.login({ email, password })
                setToken(response.accessToken)
                navigate("/dashboard/home")
                toast.success("登录成功")
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'register') {
            if (!email) {
                setErrors({ email: "请输入邮箱" });
                setIsLoading(false);
                return;
            }
            try {
                await emailApi.sendEmail(email, 'register');
                setStep('verify');
                toast.success("验证码已发送到您的邮箱");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'verify') {
            if (!otp) {
                setErrors({ otp: "请输入验证码" });
                setIsLoading(false);
                return;
            }
            try {
                await emailApi.verifyCode(email, otp);
                setStep('setup-password');
                toast.success("邮箱验证成功");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'setup-password') {
            if (!password) {
                setErrors({ password: "请输入密码" });
                setIsLoading(false);
                return;
            }

            if (!isPasswordValid) {
                setErrors({ password: "密码不符合要求" });
                setIsLoading(false);
                return;
            }

            try {
                const response = await userApi.setupPassword(email, password, otp);
                setToken(response.accessToken);
                navigate("/dashboard/home");
                toast.success("账号创建成功");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'forget-password') {
            if (!email) {
                setErrors({ email: "请输入邮箱" });
                setIsLoading(false);
                return;
            }
            try {
                await emailApi.sendEmail(email, 'forgot-password');
                setStep('forget-verify');
                toast.success("验证码已发送到您的邮箱");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'forget-verify') {
            if (!otp) {
                setErrors({ otp: "请输入验证码" });
                setIsLoading(false);
                return;
            }
            try {
                await emailApi.verifyCode(email, otp);
                setStep('reset-password');
                toast.success("邮箱验证成功");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        } else if (step === 'reset-password') {
            if (!password) {
                setErrors({ password: "请输入密码" });
                setIsLoading(false);
                return;
            }

            if (!isPasswordValid) {
                setErrors({ password: "密码不符合要求" });
                setIsLoading(false);
                return;
            }

            try {
                const response = await userApi.forgetPassword(email, password, otp);
                setToken(response.accessToken);
                navigate("/dashboard/home");
                toast.success("密码重置成功");
            } catch (error: any) {
                setErrors({ general: error.message });
            }
        }

        setIsLoading(false);
    };

    const renderPasswordStrength = () => {
        const requirements = [
            { label: "至少8个字符", met: passwordStrength.hasMinLength },
            { label: "至少一个大写字母", met: passwordStrength.hasUpperCase },
            { label: "至少一个小写字母", met: passwordStrength.hasLowerCase },
            { label: "至少一个数字", met: passwordStrength.hasNumber },
            { label: "至少一个特殊字符", met: passwordStrength.hasSpecialChar },
        ];

        return (
            <div className="space-y-3 mt-4 p-3 bg-muted/20 rounded-lg">
                <div className="text-sm font-medium text-muted-foreground/90">密码要求：</div>
                <div className="space-y-2">
                    {requirements.map((req, index) => (
                        <div key={index} className="flex items-center space-x-2">
                            {req.met ? (
                                <Check className="w-3.5 h-3.5 text-green-500/90" />
                            ) : (
                                <X className="w-3.5 h-3.5 text-destructive/80" />
                            )}
                            <span className={cn(
                                "text-sm transition-colors duration-200",
                                req.met ? "text-green-600/90" : "text-muted-foreground/70"
                            )}>
                                {req.label}
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderForm = () => {
        switch (step) {
            case 'login':
                return (
                    <>
                        <div className="space-y-5">
                            <div className="space-y-2.5">
                                <Label htmlFor="email" className="text-sm font-medium text-foreground/90">邮箱</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    autoComplete="username"
                                    className={cn(
                                        "h-11 bg-white/80 border-muted/30 transition-all duration-200",
                                        "focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                                        errors.email && "border-destructive/50 focus:ring-destructive/20"
                                    )}
                                />
                                {errors.email && (
                                    <p className="text-sm text-destructive/90 mt-1.5">{errors.email}</p>
                                )}
                            </div>

                            <div className="space-y-2.5">
                                <Label htmlFor="password" className="text-sm font-medium text-foreground/90">密码</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    autoComplete="current-password"
                                    className={cn(
                                        "h-11 bg-white/80 border-muted/30 transition-all duration-200",
                                        "focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                                        errors.password && "border-destructive/50 focus:ring-destructive/20"
                                    )}
                                />
                                {errors.password && (
                                    <p className="text-sm text-destructive/90 mt-1.5">{errors.password}</p>
                                )}
                            </div>
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <Checkbox id="remember" className="border-muted/40" />
                                <label
                                    htmlFor="remember"
                                    className="text-sm font-medium text-muted-foreground/80 select-none cursor-pointer"
                                >
                                    记住我
                                </label>
                            </div>

                            <button
                                type="button"
                                onClick={() => {
                                    setStep('forget-password');
                                    setPassword("");
                                    setErrors({});
                                }}
                                className="text-sm font-medium text-primary/90 hover:text-primary transition-colors"
                            >
                                忘记密码？
                            </button>
                        </div>

                        <Button
                            type="submit"
                            className={cn(
                                "w-full h-11 bg-primary hover:bg-primary-hover transition-all duration-200",
                                "text-white font-medium shadow-sm shadow-primary/20"
                            )}
                            disabled={isLoading}
                        >
                            {isLoading ? "登录中..." : "登录"}
                        </Button>
                    </>
                );
            case 'forget-password':
            case 'register':
                return (
                    <>
                        <div className="space-y-5">
                            <div className="space-y-2.5">
                                <Label htmlFor="email" className="text-sm font-medium text-foreground/90">邮箱</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    autoComplete="username"
                                    className={cn(
                                        "h-11 bg-white/80 border-muted/30 transition-all duration-200",
                                        "focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                                        errors.email && "border-destructive/50 focus:ring-destructive/20"
                                    )}
                                />
                                {errors.email && (
                                    <p className="text-sm text-destructive/90 mt-1.5">{errors.email}</p>
                                )}
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className={cn(
                                "w-full h-11 bg-primary hover:bg-primary-hover transition-all duration-200",
                                "text-white font-medium shadow-sm shadow-primary/20"
                            )}
                            disabled={isLoading}
                        >
                            {isLoading ? "发送中..." : "发送验证码"}
                        </Button>
                    </>
                );
            case 'forget-verify':
            case 'verify':
                return (
                    <>
                        <div className="space-y-5">
                            <div className="space-y-2.5">
                                <Label className="text-sm font-medium text-foreground/90">输入验证码</Label>
                                <InputOTP
                                    maxLength={6}
                                    value={otp}
                                    onChange={(value) => setOtp(value)}
                                    pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                                    className="gap-2"
                                >
                                    <InputOTPGroup className="gap-2">
                                        <InputOTPSlot
                                            index={0}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                        <InputOTPSlot
                                            index={1}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                        <InputOTPSlot
                                            index={2}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                    </InputOTPGroup>
                                    <InputOTPSeparator className="mx-1" />
                                    <InputOTPGroup className="gap-2">
                                        <InputOTPSlot
                                            index={3}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                        <InputOTPSlot
                                            index={4}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                        <InputOTPSlot
                                            index={5}
                                            className={cn(
                                                "h-11 w-11 bg-white/80 border-muted/30 rounded-md",
                                                "focus:ring-2 focus:ring-primary/20 focus:border-primary/30"
                                            )}
                                        />
                                    </InputOTPGroup>
                                </InputOTP>
                                {errors.otp && (
                                    <p className="text-sm text-destructive/90 mt-1.5">{errors.otp}</p>
                                )}
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className={cn(
                                "w-full h-11 bg-primary hover:bg-primary-hover transition-all duration-200",
                                "text-white font-medium shadow-sm shadow-primary/20"
                            )}
                            disabled={isLoading}
                        >
                            {isLoading ? "验证中..." : "验证"}
                        </Button>
                    </>
                );
            case 'reset-password':
            case 'setup-password':
                return (
                    <>
                        <div className="space-y-5">
                            <div className="space-y-2.5">
                                <Label htmlFor="password" className="text-sm font-medium text-foreground/90">
                                    {step === 'reset-password' ? '新密码' : '设置密码'}
                                </Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    autoComplete="current-password"
                                    className={cn(
                                        "h-11 bg-white/80 border-muted/30 transition-all duration-200",
                                        "focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                                        errors.password && "border-destructive/50 focus:ring-destructive/20"
                                    )}
                                />
                                {errors.password && (
                                    <p className="text-sm text-destructive/90 mt-1.5">{errors.password}</p>
                                )}
                                {renderPasswordStrength()}
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className={cn(
                                "w-full h-11 bg-primary hover:bg-primary-hover transition-all duration-200",
                                "text-white font-medium shadow-sm shadow-primary/20"
                            )}
                            disabled={isLoading || !isPasswordValid}
                        >
                            {isLoading ? (step === 'reset-password' ? "重置中..." : "设置中...") :
                                (step === 'reset-password' ? "重置密码" : "完成注册")}
                        </Button>
                    </>
                );
        }
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50/40 via-white to-purple-50/40 relative px-4 md:px-8">
            <div className="fixed inset-0 w-full h-full overflow-hidden pointer-events-none">
                <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-300/30 rounded-full mix-blend-multiply filter blur-3xl opacity-50 animate-blob"></div>
                <div className="absolute -top-4 -right-4 w-72 h-72 bg-yellow-300/30 rounded-full mix-blend-multiply filter blur-3xl opacity-50 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300/30 rounded-full mix-blend-multiply filter blur-3xl opacity-50 animate-blob animation-delay-4000"></div>
            </div>

            <div className="relative z-10 mb-12">
                <h1 className="text-4xl font-bold tracking-tight text-primary">
                    Hispread AI
                </h1>
            </div>

            <div className="w-full max-w-[400px] relative z-10 px-4">
                <div className="space-y-2 mb-10">
                    <h2 className="text-2xl font-semibold tracking-tight text-center">
                        {step === 'login' ? '欢迎回来' :
                            step === 'forget-password' ? '重置密码' :
                                step === 'forget-verify' ? '验证邮箱' :
                                    step === 'reset-password' ? '新密码' :
                                        '创建账号'}
                    </h2>
                    <p className="text-base text-muted-foreground/80 text-center">
                        {step === 'login' ? '请输入您的账号密码以继续' :
                            step === 'forget-password' ? '输入您的邮箱以重置密码' :
                                step === 'forget-verify' ? '输入发送到您邮箱的验证码' :
                                    step === 'reset-password' ? '为您的账号选择一个强密码' :
                                        step === 'register' ? '输入您的邮箱以开始' :
                                            step === 'verify' ? '输入发送到您邮箱的验证码' :
                                                '设置您的密码以完成注册'}
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {errors.general && (
                        <div className="flex items-center p-3 rounded bg-destructive/8 text-destructive text-sm border border-destructive/20">
                            <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                            <div>{errors.general}</div>
                        </div>
                    )}

                    {renderForm()}

                    {step === 'login' && (
                        <>
                            <div className="relative">
                                <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t border-muted/60"></div>
                                </div>
                                <div className="relative flex justify-center text-xs uppercase">
                                    <span className="bg-gradient-to-br from-blue-50/40 via-white to-purple-50/40 px-4 text-muted-foreground/60">
                                        或继续使用
                                    </span>
                                </div>
                            </div>
                        </>
                    )}
                </form>

                <div className="mt-8 space-y-3">
                    <p className="text-center text-sm text-muted-foreground/80">
                        {step === 'login' ? (
                            <>
                                没有账号？{" "}
                                <button
                                    onClick={() => {
                                        setStep('register');
                                        setErrors({});
                                    }}
                                    className="font-medium text-primary hover:text-primary-hover transition-colors"
                                >
                                    注册
                                </button>
                            </>
                        ) : (
                            <>
                                返回{" "}
                                <button
                                    onClick={() => {
                                        setStep('login');
                                        setPassword("");
                                        setOtp("");
                                        setErrors({});
                                    }}
                                    className="font-medium text-primary hover:text-primary-hover transition-colors"
                                >
                                    登录
                                </button>
                            </>
                        )}
                    </p>
                    <p className="text-center text-xs text-muted-foreground/60">
                        继续操作即表示您同意我们的{" "}
                        <a href="/privacy" className="font-medium text-primary/80 hover:text-primary transition-colors">
                            隐私政策
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;