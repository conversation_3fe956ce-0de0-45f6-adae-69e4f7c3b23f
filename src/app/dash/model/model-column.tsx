import type { OrganizationModelResponse } from "@/api/model/model-model"
import type { ColumnDef } from "@tanstack/react-table"

import { Anthropic, Google, Azure, OpenAI, XAI, DeepSeek, Mistral, Claude } from '@lobehub/icons';
import { match } from 'ts-pattern';

export const modelColumn: ColumnDef<OrganizationModelResponse>[] = [
    // {
    //     id: "id",
    //     header: "ID",
    //     cell: ({ row }) => {
    //         return <div>{row.original.id}</div>
    //     }
    // },
    {
        id: "manufacturer",
        header: "厂商",
        cell: ({ row }) => {
            const manufacturer = row.original.manufacturer
            return <div className="flex items-center gap-2">
                {
                    match(manufacturer.toLocaleLowerCase())
                        .with("openai", () => <OpenAI />)
                        .with("anthropic", () => <Anthropic />)
                        .with("claude", () => <Claude.Color />)
                        .with("x", () => <XAI />)
                        .with("azure", () => <Azure.Color />)
                        .with("google", () => <Google.Color />)
                        .with("deepseek", () => <DeepSeek.Color />)
                        .with("mistral", () => <Mistral.Color />)
                        .otherwise(() => manufacturer)
                }
                {manufacturer}
            </div>
        }
    },
    {
        id: "modelName",
        header: "模型名称",
        cell: ({ row }) => {
            return <div>{row.original.modelName}</div>
        }
    },
    {
        id: "rpm",
        header: "每分钟请求数",
        cell: ({ row }) => {
            return <div>{row.original.rpm?.toLocaleString()}</div>
        }
    },
    {
        id: "tpm",
        header: "每分钟token数",
        cell: ({ row }) => {
            return <div>{row.original.tpm?.toLocaleString()}</div>
        }
    }
]