import { DataTable } from "@/components/table/base-table";
import { Separator } from "@/components/ui/separator";
import { modelColumn } from "./model-column";
import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { modelApi } from "@/api/model/model-api";
import { useAtomValue } from "jotai";
import { organizationState } from "@/state/user-state";
import { Input } from "@/components/ui/input";
import useSearchParamsManager from "@/hooks/use-url-param";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { pushModal } from "@/components/modals";


export default function ModelPage() {


    const organization = useAtomValue(organizationState)
    const { addParam, getParam } = useSearchParamsManager();


    return (
        <div className="flex flex-col gap-2 mx-auto bg-white rounded-lg shadow-borders-base shadow-sm border border-zinc-200 py-4">
            <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                {/* left */}
                <div>
                    <h1 className="text-[18px]">模型</h1>
                    <h2 className="text-[13px] text-zinc-500">
                        <span className="text-zinc-500 text-[13px]">
                            管理模型、版本和部署
                        </span>
                    </h2>
                </div>
                {/* right */}
                <div className="pr-4">
                    {/* <CommonButton>
                        <PlusIcon className="w-4 h-4" />
                        新建模型
                    </CommonButton> */}
                </div>
            </header>
            <Separator />
            <DataTable
                columns={modelColumn}
                isNeedSelect={true}
                dependencies={[organization]}
                onFetch={async (params) => modelApi.pageModel(organization?.organizationId, params)}
                toolbar={(table, tableId) => {
                    return <DataTableToolbar table={table} tableId={tableId}>
                        <ToolbarLeft>
                            <Input placeholder="搜索模型" className="max-w-56" value={getParam("modelName") || ""} onChange={(e) => {
                                addParam("modelName", e.target.value)
                            }} />
                        </ToolbarLeft>
                        {
                            table.getFilteredSelectedRowModel().rows.length > 0 && (
                                <FloatingMultiActionBar
                                    selectedCount={table.getFilteredSelectedRowModel().rows.length}
                                    selectedItems={table.getFilteredSelectedRowModel().rows.map((row: any) => row.original)}
                                    actions={[{
                                        id: "edit",
                                        label: "编辑",
                                        shortcut: "E",
                                        onClick: (selectedItems) => {
                                            const itemsToEdit = [...selectedItems];
                                            pushModal("EditModelModal", { selectedItems: itemsToEdit })
                                            table.resetRowSelection()
                                        }
                                    }
                                    ]}
                                />
                            )
                        }
                    </DataTableToolbar>
                }}
            />
        </div>
    )
}