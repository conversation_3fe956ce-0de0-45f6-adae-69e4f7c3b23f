import { DataTable } from "@/components/table/base-table";
import { Separator } from "@/components/ui/separator";
import { llmPriceColumn } from "./llm-price-column";
import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { llmPriceApi } from "@/api/llm-price/llm-price-api";
import { useAtomValue } from "jotai";
import { organizationState } from "@/state/user-state";
import { Input } from "@/components/ui/input";
import useSearchParamsManager from "@/hooks/use-url-param";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { pushModal } from "@/components/modals";
import { PlusIcon } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PRICE_STATUS_OPTIONS } from "@/api/llm-price/llm-price-model";
import { refreshTable<PERSON>tom } from "@/state/table-state";
import { useSetAtom } from "jotai";
import CommonButton from "@/components/ui/button/new-button";

export default function LlmPricePage() {
    const organization = useAtomValue(organizationState);
    const { addParam, getParam } = useSearchParamsManager();
    const refreshTable = useSetAtom(refreshTableAtom);

    const handleCreate = () => {
        pushModal("CreateLlmPriceModal", {
            onClose: () => {
                // refreshTable((prev) => prev + 1);
            },
            onSuccess: () => {
                refreshTable((prev) => prev + 1);
            }
        });
    };

    const handleDelete = (selectedIds: number[]) => {
        pushModal("AlertModal", {
            title: "确认删除",
            description: `确定要删除选中的 ${selectedIds.length} 条价格记录吗？`,
            onConfirm: async () => {
                await Promise.all(selectedIds.map((id) => llmPriceApi.delete(id)));
                refreshTable((prev) => prev + 1);
            },
        });
    };

    return (
        <div className="flex flex-col gap-2 mx-auto bg-white rounded-lg shadow-borders-base shadow-sm border border-zinc-200 py-4">
            <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                {/* left */}
                <div>
                    <h1 className="text-[18px]">LLM价格管理</h1>
                    <h2 className="text-[13px] text-zinc-500">
                        <span className="text-zinc-500 text-[13px]">
                            管理不同模型的定价策略，包括文本、音频、图像和推理等多模态价格配置
                        </span>
                    </h2>
                </div>
                {/* right */}
                <div className="pr-4">
                    <CommonButton onClick={handleCreate} size="sm" variant="primary" className="text-xs">
                        <PlusIcon className="w-4 h-4 mr-1" />
                        新增价格
                    </CommonButton>
                </div>
            </header>
            <Separator />
            <DataTable
                columns={llmPriceColumn}
                isNeedSelect={true}
                dependencies={[organization]}
                onFetch={async (params) => {
                    return llmPriceApi.queryPage({
                        page: params.pagination.pageIndex + 1,
                        size: params.pagination.pageSize,
                        systemModelId: getParam("systemModelId") ? Number(getParam("systemModelId")) : undefined,
                        statuses: getParam("statuses") ? Number(getParam("statuses")) : undefined,
                    });
                }}
                toolbar={(table, tableId) => {
                    return (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <Input
                                    placeholder="搜索系统模型ID"
                                    className="max-w-56 text-xs"
                                    value={getParam("systemModelId") || ""}
                                    onChange={(e) => {
                                        if (e.target.value) {
                                            addParam("systemModelId", e.target.value);
                                        } else {
                                            addParam("systemModelId", "");
                                        }
                                    }}
                                />
                                <Select
                                    value={getParam("statuses") || "all"}
                                    onValueChange={(value) => {
                                        if (value === "all") {
                                            addParam("statuses", "");
                                        } else {
                                            addParam("statuses", value);
                                        }
                                    }}
                                >
                                    <SelectTrigger className="w-32 text-xs">
                                        <SelectValue placeholder="状态" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all" className="text-zinc-500 text-xs">全部状态</SelectItem>
                                        {PRICE_STATUS_OPTIONS.map((option) => (
                                            <SelectItem className="text-zinc-500 text-xs" key={option.value} value={String(option.value)}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </ToolbarLeft>
                        </DataTableToolbar>
                    );
                }}
                floatingBar={(table) => (
                    <FloatingMultiActionBar
                        selectedCount={table.getSelectedRowModel().rows.length}
                        selectedItems={table.getSelectedRowModel().rows}
                        actions={[
                            {
                                label: "删除",
                                onClick: (selectedRows) => {
                                    const selectedIds = selectedRows.map((row) => row.original.id);
                                    handleDelete(selectedIds);
                                },
                                id: "delete",
                                shortcut: "D",
                            },
                        ]}
                    />
                )}
            />
        </div>
    );
}