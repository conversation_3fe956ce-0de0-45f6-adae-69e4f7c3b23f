import type { ColumnDef } from "@tanstack/react-table";
import { PRICE_STATUS_OPTIONS } from "@/api/llm-price/llm-price-model";
import type { LlmPriceResponse } from "@/api/llm-price/llm-price-model";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { pushModal } from "@/components/modals";
import { EditIcon, EyeIcon } from "lucide-react";
import { refreshTableAtom } from "@/state/table-state";
import { useSetAtom } from "jotai";
import dayjs from "dayjs";

export const llmPriceColumn: ColumnDef<LlmPriceResponse>[] = [
    {
        id: "id",
        accessorKey: "id",
        header: "ID",
        size: 80,
        cell: ({ row }) => row.getValue("id"),
    },
    {
        id: "systemModelName",
        accessorKey: "systemModelName",
        header: "系统模型",
        size: 200,
        cell: ({ row }) => {
            const modelName = row.original.systemModelName;
            const manufacturerName = row.original.manufacturerName;
            return (
                <div className="flex flex-row gap-2 truncate">
                    {manufacturerName && (
                        <Badge variant="outline" className="text-xs text-zinc-500">{manufacturerName}</Badge>
                    )}
                    <span className="font-medium text-xs">{modelName || "未知模型"}</span>
                </div>
            );
        },
    },
    {
        id: "statuses",
        accessorKey: "statuses",
        header: "状态",
        size: 100,
        cell: ({ row }) => {
            const status = row.getValue("statuses") as number;
            const statusOption = PRICE_STATUS_OPTIONS.find((option) => option.value === status);
            const variant = status === 1 ? "default" : status === 0 ? "secondary" : "destructive";
            return (
                <Badge variant={variant}>{statusOption?.label || `状态${status}`}</Badge>
            );
        },
    },
    {
        id: "pricing",
        header: "价格概览",
        size: 150,
        cell: ({ row }) => {
            const hasTextPricing = row.original.textPrompt || row.original.textCompletion;
            const hasAudioPricing = row.original.audioPrompt || row.original.audioCompletion;
            const hasImagePricing = row.original.imagePrompt || row.original.imageCompletion;
            const hasReasoningPricing = row.original.reasoningCompletion;

            return (
                <div className="flex gap-1">
                    {hasTextPricing && <Badge variant="outline" className="text-xs">文本</Badge>}
                    {hasAudioPricing && <Badge variant="outline" className="text-xs">音频</Badge>}
                    {hasImagePricing && <Badge variant="outline" className="text-xs">图像</Badge>}
                    {hasReasoningPricing && <Badge variant="outline" className="text-xs">推理</Badge>}
                </div>
            );
        },
    },
    {
        id: "createTime",
        accessorKey: "createTime",
        header: "创建时间",
        size: 180,
        cell: ({ row }) => {
            const createTime = row.getValue("createTime") as number | undefined;
            return createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss") : "-";
        },
    },
    {
        id: "actions",
        header: "操作",
        size: 120,
        cell: ({ row }) => {
            const refreshTable = useSetAtom(refreshTableAtom);

            const handleView = () => {
                pushModal("ViewLlmPriceModal", {
                    priceId: row.original.id,
                    onClose: () => {
                        // refreshTable((prev) => prev + 1);
                    },
                });
            };

            const handleEdit = () => {
                pushModal("EditLlmPriceModal", {
                    priceId: row.original.id,
                    onClose: () => {
                        // refreshTable((prev) => prev + 1);
                    },
                    onSuccess: () => {
                        refreshTable((prev) => prev + 1);
                    },
                });
            };
            return (
                <div className="flex gap-1">
                    <Button size="sm" variant="ghost" onClick={handleView}>
                        <EyeIcon className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" onClick={handleEdit}>
                        <EditIcon className="h-4 w-4" />
                    </Button>
                </div>
            );
        },
    },
];