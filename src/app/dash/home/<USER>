import UsageCharts from "@/components/charts/usage-charts";
import TokenUsageCharts from "@/components/charts/token-usage-charts";
import RequestUsageCharts from "@/components/charts/request-usage-charts";
import MonthPicker from "@/components/ui/date/month-picker";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useState } from "react";

export default function HomePage() {
    const currentDate = new Date();
    const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear());
    const [selectedMonth, setSelectedMonth] = useState(currentDate.getMonth() + 1); // 月份从1开始

    const handleDateChange = (year: number, month: number) => {
        setSelectedYear(year);
        setSelectedMonth(month);
    };

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-2 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    {/* left */}
                    <div>
                        <h1 className="text-[18px]">主页</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            <span className="text-zinc-500 text-[13px]">
                                可视化数据分析平台数据
                            </span>
                        </h2>
                    </div>
                    {/* right */}
                    <div className="pr-4">
                        <MonthPicker onDateChange={handleDateChange} />
                    </div>
                </header>
                <Separator />
                <div className="flex-grow p-4">
                    <Tabs defaultValue="amount_usage" className="h-full flex flex-col">
                        <TabsList className="mb-4">
                            <TabsTrigger value="amount_usage">Amount</TabsTrigger>
                            <TabsTrigger value="token_usage">Token</TabsTrigger>
                            <TabsTrigger value="request_usage">Request</TabsTrigger>
                        </TabsList>
                        <TabsContent value="amount_usage" className="flex-grow">
                            <UsageCharts selectedYear={selectedYear} selectedMonth={selectedMonth} />
                        </TabsContent>
                        <TabsContent value="token_usage" className="flex-grow">
                            <TokenUsageCharts selectedYear={selectedYear} selectedMonth={selectedMonth} />
                        </TabsContent>
                        <TabsContent value="request_usage" className="flex-grow">
                            <RequestUsageCharts selectedYear={selectedYear} selectedMonth={selectedMonth} />
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    )
}