import { type ColumnDef } from "@tanstack/react-table";
import { type CommonTokenKey, type CommonKeyStatus } from "@/api/key-management/common-key-model";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { MoreHorizontal, Edit, Trash2, FlaskConical, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { pushModal } from "@/components/modals";
import { toast } from "sonner";
import { commonKeyApi } from "@/api/key-management/common-key-api";
import { useSetAtom } from "jotai";
import { refreshTable<PERSON>tom } from "@/state/table-state";
import { CopyButton } from "@/components/ui/button/copy-button";

export const commonKeyColumn: ColumnDef<CommonTokenKey>[] = [
    {
        id: "name",
        header: "密钥名称",
        cell: ({ row }) => {
            const name = row.original.name;
            return (
                <div className="font-medium max-w-[150px] truncate" title={name || `密钥-${row.original.id}`}>
                    {name || `密钥-${row.original.id}`}
                </div>
            );
        }
    },
    {
        id: "accessToken",
        header: "访问令牌",
        cell: ({ row }) => {
            const accessToken = row.original.accessToken;
            return (
                <div className="flex items-center gap-2 max-w-[100px]">
                    <code className="font-mono text-xs text-muted-foreground truncate">
                        {accessToken ? accessToken.substring(0, 20) + "..." : "未设置"}
                    </code>
                    {accessToken && <CopyButton text={accessToken} />}
                </div>
            );
        }
    },
    {
        id: "type",
        header: "类型",
        size: 120,
        minSize: 100,
        cell: ({ row }) => {
            const type = row.original.type;
            const accountType = row.original.claudeCodeRefreshInfo?.accountType;

            if (type === "ClaudeCode" && accountType) {
                return (
                    <Badge variant="outline" className="gap-1.5 max-w-[120px]">
                        <span
                            className={cn(
                                "size-1.5 rounded-full",
                                accountType === "PRO" ? "bg-purple-500" :
                                    accountType === "MAX_100" ? "bg-blue-500" :
                                        accountType === "MAX_200" ? "bg-green-500" : "bg-gray-500"
                            )}
                            aria-hidden="true"
                        />
                        <span className="truncate">
                            {accountType === "PRO" ? "专业版" :
                                accountType === "MAX_100" ? "Max 100" :
                                    accountType === "MAX_200" ? "Max 200" : "未知"}
                        </span>
                    </Badge>
                );
            }

            return (
                <Badge variant="outline" className="max-w-[120px]">
                    <span className="truncate">{type}</span>
                </Badge>
            );
        }
    },
    {
        id: "quota",
        header: "配额",
        size: 100,
        minSize: 80,
        cell: ({ row }) => {
            const quota = row.original.quota;
            return (
                <div className="font-medium max-w-[100px] truncate">
                    {quota != null ? `$${quota.toFixed(2)}` : "-"}
                </div>
            );
        }
    },
    {
        id: "status",
        header: "状态",
        size: 100,
        minSize: 80,
        cell: ({ row }) => {
            const status = row.original.status;
            const getStatusConfig = (status: CommonKeyStatus) => {
                switch (status) {
                    case 'ACTIVE':
                        return { color: "bg-green-500", text: "活跃" };
                    case 'DISABLED':
                        return { color: "bg-gray-500", text: "已禁用" };
                    case 'AUTH_FAILED':
                        return { color: "bg-red-500", text: "认证失败" };
                    default:
                        return { color: "bg-gray-500", text: "未知" };
                }
            };

            const config = getStatusConfig(status);
            return (
                <Badge variant="outline" className="gap-1.5 text-zinc-600 max-w-[100px]">
                    <span
                        className={cn("size-1.5 rounded-full", config.color)}
                        aria-hidden="true"
                    />
                    <span className="truncate">{config.text}</span>
                </Badge>
            );
        }
    },
    {
        id: "createdAt",
        header: "创建时间",
        size: 140,
        minSize: 120,
        cell: ({ row }) => {
            const createdAt = row.original.createdAt;
            const date = new Date(createdAt);
            return (
                <div className="max-w-[100px] truncate text-xs" title={date.toLocaleString()}>
                    {date.toLocaleDateString()} {date.toLocaleTimeString().slice(0, 5)}
                </div>
            );
        }
    },
    {
        id: "actions",
        header: "操作",
        size: 80,
        minSize: 60,
        cell: function Cell({ row }) {
            const refreshTable = useSetAtom(refreshTableAtom);
            const key = row.original;

            const handleDelete = async () => {
                try {
                    await commonKeyApi.deleteKey(key.id);
                    toast.success("密钥删除成功");
                    refreshTable((prev) => prev + 1);
                } catch {
                    toast.error("密钥删除失败");
                }
            };

            const handleTest = () => {
                pushModal("TestKeyModal", {
                    keyData: key,
                    onClose: () => { }
                });
            };

            const handleFlushToken = async () => {
                if (key.type !== "ClaudeCode" && key.type !== "GoogleVertexAI") {
                    toast.error("只有 Claude Code 和 Google Vertex AI 密钥支持刷新令牌");
                    return;
                }

                try {
                    if (key.type === "ClaudeCode") {
                        await commonKeyApi.flushClaudeCodeToken(key.id);
                    } else if (key.type === "GoogleVertexAI") {
                        await commonKeyApi.flushGoogleVertexAIToken(key.id);
                    }
                    toast.success("令牌刷新成功");
                    refreshTable((prev) => prev + 1);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : "令牌刷新失败";
                    toast.error(errorMessage);
                }
            };

            const handleEdit = () => {
                pushModal("EditKeyModal", {
                    keyData: key,
                    onClose: () => { },
                    onSuccess: () => { refreshTable((prev) => prev + 1); }
                });
            };

            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button size="icon" variant="ghost" className="h-8 w-8">
                            <MoreHorizontal className="size-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleTest}>
                            <FlaskConical className="mr-2 size-4" />
                            测试密钥
                        </DropdownMenuItem>
                        {key.type === "ClaudeCode" && (
                            <DropdownMenuItem onClick={handleFlushToken}>
                                <RefreshCw className="mr-2 size-4" />
                                刷新Token
                            </DropdownMenuItem>
                        )}
                        {key.type === "GoogleVertexAI" && (
                            <DropdownMenuItem onClick={handleFlushToken}>
                                <RefreshCw className="mr-2 size-4" />
                                刷新Token
                            </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={handleEdit}>
                            <Edit className="mr-2 size-4" />
                            编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                            <Trash2 className="mr-2 size-4" />
                            删除
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        }
    },
];