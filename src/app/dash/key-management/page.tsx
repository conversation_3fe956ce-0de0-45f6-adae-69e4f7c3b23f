import { DataTable } from "@/components/table/base-table";
import CommonButton from "@/components/ui/button/new-button";
import { PlusIcon } from "lucide-react";
import { commonKeyColumn } from "./common-key-column";
import { commonKeyApi } from "@/api/key-management/common-key-api";
import { DataTableToolbar, ToolbarLeft, ToolbarItem } from "@/components/table/toolbar/data-table-toolbar";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { refreshTableAtom } from "@/state/table-state";
import { pushModal } from "@/components/modals";
import { toast } from "sonner";
import { useSetAtom } from "jotai";
import { Input } from "@/components/ui/input";
import useSearchParamsManager from "@/hooks/use-url-param";
import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function KeyManagementPage() {
    const setRefreshTable = useSetAtom(refreshTableAtom);
    const { getParam, addParam } = useSearchParamsManager();

    // 处理中文输入法的状态
    const [isComposing, setIsComposing] = useState(false);
    const [inputValue, setInputValue] = useState(getParam("name") || "");

    // 同步URL参数变化到本地状态
    useEffect(() => {
        if (!isComposing) {
            setInputValue(getParam("name") || "");
        }
    }, [getParam("name"), isComposing]);

    const handleCreateSuccess = () => {
        // 刷新表格数据
        setRefreshTable(prev => prev + 1);
    };

    return (
        <>
            <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
                <div className="flex flex-col gap-2 h-full">
                    <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                        {/* left */}
                        <div className="min-w-0 flex-1">
                            <h1 className="text-[18px] truncate">密钥管理</h1>
                            <h2 className="text-[13px] text-zinc-500">
                                <span className="text-zinc-500 text-[13px]">
                                    管理Claude Code访问密钥和刷新令牌
                                </span>
                            </h2>
                        </div>
                        {/* right */}
                        <div className="pr-4 flex-shrink-0">
                            <CommonButton onClick={() => pushModal("CreateKeyModal", {
                                onClose: () => { },
                                onSuccess: handleCreateSuccess
                            })}>
                                <PlusIcon className="w-4 h-4" />
                                <span className="hidden sm:inline ml-1">新建密钥</span>
                                <span className="sm:hidden">新建</span>
                            </CommonButton>
                        </div>
                    </header>
                    {/* <Separator /> */}
                    <div className="flex-1 min-h-0">
                        <DataTable
                            columns={commonKeyColumn}
                            isNeedSelect={true}
                            onFetch={async (params) => commonKeyApi.pageKeys(params)}
                            className="w-full min-w-0"
                            toolbar={(table, tableId) => {
                                return <DataTableToolbar table={table} tableId={tableId}>
                                    <ToolbarLeft>
                                        <ToolbarItem>
                                            <Input
                                                placeholder="密钥名称"
                                                className="max-w-40 sm:max-w-56 text-xs"
                                                value={inputValue}
                                                onChange={(e) => {
                                                    setInputValue(e.target.value);
                                                    // 只有在不是中文输入法组合状态时才更新URL参数
                                                    if (!isComposing) {
                                                        addParam("name", e.target.value);
                                                    }
                                                }}
                                                onCompositionStart={() => {
                                                    setIsComposing(true);
                                                }}
                                                onCompositionEnd={(e) => {
                                                    setIsComposing(false);
                                                    // 中文输入完成后更新URL参数
                                                    addParam("name", e.currentTarget.value);
                                                }}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <Input
                                                placeholder="邮箱地址"
                                                className="max-w-40 sm:max-w-56 text-xs"
                                                value={getParam("email") || ""}
                                                onChange={(e) => {
                                                    addParam("email", e.target.value);
                                                }}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <Select
                                                value={getParam("status") || "all"}
                                                onValueChange={(value) => {
                                                    if (value === "all") {
                                                        addParam("status", "");
                                                    } else {
                                                        addParam("status", value);
                                                    }
                                                }}
                                            >
                                                <SelectTrigger className="w-24 sm:w-32 text-xs">
                                                    <SelectValue placeholder="状态" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all" className="text-xs">全部状态</SelectItem>
                                                    <SelectItem value={'ACTIVE'} className="text-xs">活跃</SelectItem>
                                                    <SelectItem value={'DISABLED'} className="text-xs">已禁用</SelectItem>
                                                    <SelectItem value={'AUTH_FAILED'} className="text-xs">认证失败</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <Select
                                                value={getParam("type") || "all"}
                                                onValueChange={(value) => {
                                                    if (value === "all") {
                                                        addParam("type", "");
                                                    } else {
                                                        addParam("type", value);
                                                    }
                                                }}
                                            >
                                                <SelectTrigger className="w-24 sm:w-40 text-xs">
                                                    <SelectValue placeholder="类型" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all" className="text-xs">全部类型</SelectItem>
                                                    <SelectItem value={'ClaudeCode'} className="text-xs">Claude Code</SelectItem>
                                                    <SelectItem value={'Openai'} className="text-xs">OpenAI</SelectItem>
                                                    <SelectItem value={'Azure'} className="text-xs">Azure</SelectItem>
                                                    <SelectItem value={'Agent'} className="text-xs">Agent</SelectItem>
                                                    <SelectItem value={'Aws'} className="text-xs">AWS</SelectItem>
                                                    <SelectItem value={'Anthropic'} className="text-xs">Anthropic</SelectItem>
                                                    <SelectItem value={'GoogleAiStudio'} className="text-xs">Google AI Studio</SelectItem>
                                                    <SelectItem value={'GoogleVertexAI'} className="text-xs">Google Vertex AI</SelectItem>
                                                    <SelectItem value={'BingSearch'} className="text-xs">Bing Search</SelectItem>
                                                    <SelectItem value={'GoogleSearch'} className="text-xs">Google Search</SelectItem>
                                                    <SelectItem value={'Ark'} className="text-xs">Ark</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </ToolbarItem>
                                    </ToolbarLeft>
                                    {
                                        table.getFilteredSelectedRowModel().rows.length > 0 && (
                                            <FloatingMultiActionBar
                                                selectedCount={table.getFilteredSelectedRowModel().rows.length}
                                                selectedItems={table.getFilteredSelectedRowModel().rows.map((row: any) => row.original)}
                                                className="text-xs"
                                                actions={[{
                                                    id: "delete",
                                                    label: "删除",
                                                    shortcut: "D",
                                                    onClick: (selectedItems) => {
                                                        const itemsToDelete = [...selectedItems];
                                                        pushModal("DoubleCheckModal", {
                                                            title: "批量删除密钥",
                                                            description: `确定要删除这 ${itemsToDelete.length} 个密钥吗？`,
                                                            onConfirm: async () => {
                                                                toast.promise(async () => {
                                                                    await commonKeyApi.batchDeleteKeys(itemsToDelete.map((item: any) => item.id));
                                                                    setRefreshTable(prev => prev + 1);
                                                                    table.resetRowSelection();
                                                                }, {
                                                                    loading: "删除中...",
                                                                    success: "批量删除密钥成功",
                                                                    error: "批量删除密钥失败"
                                                                });
                                                            }
                                                        });
                                                    }
                                                }]
                                                }
                                            />
                                        )
                                    }
                                </DataTableToolbar>
                            }}
                        />
                    </div>
                </div>
            </div>
        </>
    );
}
