import { DataTable } from "@/components/table/base-table";
import { Separator } from "@/components/ui/separator";
import { organizationColumn } from "./organization-column";
import { organizationState } from "@/state/user-state";
import { useAtomValue, useSetAtom } from "jotai";
import { organizationApi } from "@/api/organization/organization-api";
import { DataTableToolbar, ToolbarItem, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { Input } from "@/components/ui/input";
import useSearchParamsManager from "@/hooks/use-url-param";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { toast } from "sonner";
import { refreshTableAtom } from "@/state/table-state";
import { pushModal } from "@/components/modals";
import { useState, useEffect } from "react";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";


export default function OrganizationPage() {

    const organization = useAtomValue(organizationState)

    const refresh = useSetAtom(refreshTableAtom)

    const { getParam, addParam } = useSearchParamsManager()

    // 处理中文输入法的状态
    const [isComposing, setIsComposing] = useState(false)
    const [inputValue, setInputValue] = useState(getParam("organizationName") || "")

    // 同步URL参数变化到本地状态
    useEffect(() => {
        if (!isComposing) {
            setInputValue(getParam("organizationName") || "")
        }
    }, [getParam("organizationName"), isComposing])

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200 py-2">
            <div className="flex flex-col gap-2 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    {/* left */}
                    <div>
                        <h1 className="text-[18px]">组织</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            <span className="text-zinc-500 text-[13px]">
                                管理组织成员、权限和组织设置
                            </span>
                        </h2>
                    </div>
                    {/* right */}
                    <div className="pr-4">
                        {/* <CommonButton onClick={() => {
                            pushModal('CreateOrganizationModal')
                        }}>
                            <PlusIcon className="w-4 h-4" />
                            新建组织
                        </CommonButton> */}
                    </div>
                </header>
                <Separator />

                <DataTable
                    columns={organizationColumn}
                    isNeedSelect={true}
                    dependencies={[organization]}
                    onFetch={async (params) => organizationApi.pageOrganization(params)}
                    defaultPageSize={50}
                    toolbar={(table, tableId) => {
                        return <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <ToolbarItem>
                                    <Input
                                        placeholder="组织名称"
                                        className="max-w-56"
                                        value={inputValue}
                                        onChange={(e) => {
                                            setInputValue(e.target.value)
                                            // 只有在不是中文输入法组合状态时才更新URL参数
                                            if (!isComposing) {
                                                addParam("organizationName", e.target.value)
                                            }
                                        }}
                                        onCompositionStart={() => {
                                            setIsComposing(true)
                                        }}
                                        onCompositionEnd={(e) => {
                                            setIsComposing(false)
                                            // 中文输入完成后更新URL参数
                                            addParam("organizationName", e.currentTarget.value)
                                        }}
                                    />
                                </ToolbarItem>
                                <ToolbarItem>
                                    <DataTableUrlFilter
                                        paramKey="statuses"
                                        title="状态"
                                        options={[{
                                            label: "启用",
                                            value: 1
                                        }, {
                                            label: "禁用",
                                            value: 0
                                        }]}
                                    />
                                </ToolbarItem>
                            </ToolbarLeft>
                            {
                                table.getFilteredSelectedRowModel().rows.length > 0 && (
                                    <FloatingMultiActionBar
                                        selectedCount={table.getFilteredSelectedRowModel().rows.length}
                                        selectedItems={table.getFilteredSelectedRowModel().rows.map((row: any) => row.original)}
                                        actions={[{
                                            id: "delete",
                                            label: "删除",
                                            shortcut: "D",
                                            onClick: (selectedItems) => {
                                                const itemsToEdit = [...selectedItems];
                                                pushModal('DoubleCheckModal', {
                                                    title: "删除组织",
                                                    description: "确定要删除这些组织吗？",
                                                    onConfirm: () => {
                                                        toast.promise(organizationApi.deleteOrganization(itemsToEdit.map((item: any) => item.organizationId)), {
                                                            loading: "删除中...",
                                                            success: () => {
                                                                table.resetRowSelection()
                                                                refresh(prev => prev + 1)
                                                                return "删除成功"
                                                            },
                                                            error: (error) => {
                                                                return '删除失败:' + error.message
                                                            }
                                                        })
                                                    }
                                                })
                                            }
                                        }
                                        ]}
                                    />
                                )
                            }
                        </DataTableToolbar>
                    }}
                />
            </div>
        </div>
    )
}