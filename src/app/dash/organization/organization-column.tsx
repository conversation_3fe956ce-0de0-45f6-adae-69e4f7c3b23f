import type { OrganizationModal } from "@/api/organization/organization-modal"
import { pushModal } from "@/components/modals"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { cn, usageDiv } from "@/lib/utils"
import type { ColumnDef } from "@tanstack/react-table"


export const organizationColumn: ColumnDef<OrganizationModal>[] = [
    {
        id: "organizationId",
        header: "ID",
        accessorKey: "organizationId",
    },
    {
        id: "organizationName",
        header: "组织名称",
        accessorKey: "organizationName",
    },
    {
        id: "statuses",
        header: "状态",
        accessorKey: "statuses",
        cell: ({ row }) => {
            return <div>
                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                    <span
                        className={cn("size-1.5 rounded-full ", row.original.statuses == 1 ? "bg-green-500" : "bg-red-500")}
                        aria-hidden="true"
                    ></span>
                    {row.original.statuses === 1 ? "启用" : "禁用"}
                </Badge></div>
        }
    },
    {
        id: "availableKeys",
        header: "可用密钥数",
        accessorKey: "availableKeys",
        cell: ({ row }) => {
            return <div>{row.original.availableKeys}</div>
        }
    },
    {
        id: "monthUsage",
        header: () => <div className="text-right">本月使用量</div>,
        accessorKey: "monthUsage",
        enableSorting: true,
        meta: {
            enableSortingIcon: true,
            defaultSortDesc: true,
        },
        cell: ({ row }) => {
            return <div className="text-right">${(Number(BigInt(row.original.monthUsage)) / usageDiv).toLocaleString('en-US', { maximumFractionDigits: 6 })}</div>
        }
    },
    {
        id: "balance",
        header: () => <div className="text-right">余额</div>,
        accessorKey: "balance",
        enableSorting: true,
        meta: {
            enableSortingIcon: true,
            defaultSortDesc: true,
        },
        cell: ({ row }) => {
            return <div className="text-right">${(Number(BigInt(row.original.balance)) / usageDiv).toLocaleString('en-US', { maximumFractionDigits: 6 })}</div>
        }
    },
    {
        id: "accumulatedUsage",
        header: () => <div className="text-right">累计使用量</div>,
        accessorKey: "accumulatedUsage",
        enableSorting: true,
        meta: {
            enableSortingIcon: true,
            defaultSortDesc: true,
        },
        cell: ({ row }) => {
            return <div className="text-right">${(Number(BigInt(row.original.accumulatedUsage)) / usageDiv).toLocaleString('en-US', { maximumFractionDigits: 6 })}</div>
        }
    },
    {
        id: "actions",
        header: "操作",
        accessorKey: "actions",
        cell: ({ row }) => {
            return <div>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                        pushModal("UpdateOrganizationModal", { organizationId: row.original.organizationId, organizationName: row.original.organizationName })
                    }}
                >
                    编辑
                </Button>
            </div>
        }
    }

]