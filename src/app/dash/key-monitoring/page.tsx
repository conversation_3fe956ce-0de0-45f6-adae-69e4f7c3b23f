import { useState, useEffect } from "react";
import { useRequest } from "ahooks";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
    Activity, 
    Key, 
    TrendingUp, 
    TrendingDown, 
    AlertTriangle, 
    CheckCircle,
    XCircle,
    Clock,
    BarChart3,
    Zap
} from "lucide-react";
import { keyMonitoringApi } from "@/api/key-monitoring/key-monitoring-api";
import type { KeyMonitoringOverview, ModelKeyStats } from "@/api/key-monitoring/key-monitoring-types";
import { Loading } from "@/components/ui/loading";
import KeyOverviewCards from "@/components/key-monitoring/key-overview-cards";
import ModelStatsTable from "@/components/key-monitoring/model-stats-table";
import KeyPerformanceCharts from "@/components/key-monitoring/key-performance-charts";

export default function KeyMonitoringPage() {
    const [selectedTab, setSelectedTab] = useState("overview");

    // 获取概览数据
    const { 
        data: overview, 
        loading: overviewLoading, 
        refresh: refreshOverview 
    } = useRequest(keyMonitoringApi.getOverview, {
        pollingInterval: 30000, // 30秒轮询
    });

    // 获取所有模型统计
    const { 
        data: modelsStats, 
        loading: modelsLoading, 
        refresh: refreshModelsStats 
    } = useRequest(keyMonitoringApi.getAllModelsStats, {
        pollingInterval: 30000,
    });

    // 获取性能趋势数据
    const { 
        data: performanceTrends, 
        loading: trendsLoading, 
        refresh: refreshTrends 
    } = useRequest(() => keyMonitoringApi.getPerformanceTrends(24), {
        pollingInterval: 60000, // 1分钟轮询
    });

    const handleRefreshAll = () => {
        refreshOverview();
        refreshModelsStats();
        refreshTrends();
    };

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-2 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    <div>
                        <h1 className="text-[18px] flex items-center gap-2">
                            <Activity className="w-5 h-5 text-blue-600" />
                            密钥监控
                        </h1>
                        <h2 className="text-[13px] text-zinc-500">
                            实时监控密钥性能、使用统计和调度器状态
                        </h2>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                            <Clock className="w-3 h-3 mr-1" />
                            实时更新
                        </Badge>
                        <button
                            onClick={handleRefreshAll}
                            className="text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50"
                        >
                            刷新数据
                        </button>
                    </div>
                </header>
                <Separator />
                
                <div className="flex-grow p-4 overflow-hidden">
                    <Tabs value={selectedTab} onValueChange={setSelectedTab} className="h-full flex flex-col">
                        <TabsList className="mb-4">
                            <TabsTrigger value="overview" className="flex items-center gap-2">
                                <BarChart3 className="w-4 h-4" />
                                概览
                            </TabsTrigger>
                            <TabsTrigger value="models" className="flex items-center gap-2">
                                <Key className="w-4 h-4" />
                                模型统计
                            </TabsTrigger>
                            <TabsTrigger value="performance" className="flex items-center gap-2">
                                <TrendingUp className="w-4 h-4" />
                                性能趋势
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="flex-grow overflow-auto">
                            {overviewLoading ? (
                                <div className="flex items-center justify-center h-64">
                                    <Loading />
                                </div>
                            ) : overview ? (
                                <KeyOverviewCards overview={overview} />
                            ) : (
                                <div className="flex items-center justify-center h-64 text-zinc-500">
                                    暂无数据
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="models" className="flex-grow overflow-auto">
                            {modelsLoading ? (
                                <div className="flex items-center justify-center h-64">
                                    <Loading />
                                </div>
                            ) : modelsStats ? (
                                <ModelStatsTable modelsStats={modelsStats} />
                            ) : (
                                <div className="flex items-center justify-center h-64 text-zinc-500">
                                    暂无数据
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="performance" className="flex-grow overflow-auto">
                            {trendsLoading ? (
                                <div className="flex items-center justify-center h-64">
                                    <Loading />
                                </div>
                            ) : performanceTrends ? (
                                <KeyPerformanceCharts trends={performanceTrends} />
                            ) : (
                                <div className="flex items-center justify-center h-64 text-zinc-500">
                                    暂无数据
                                </div>
                            )}
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    );
}
