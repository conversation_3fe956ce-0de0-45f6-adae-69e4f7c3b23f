import { DataTable } from "@/components/table/base-table";
import CommonButton from "@/components/ui/button/new-button";
import { PlusIcon } from "lucide-react";
import { secretColumn } from "./secret-column";
import { secretApi } from "@/api/secret/secret-api";
import { useAtomValue, useSetAtom } from "jotai";
import { organizationState } from "@/state/user-state";
import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { refreshTableAtom } from "@/state/table-state";
import { pushModal } from "@/components/modals";
import { toast } from "sonner";

export default function SecretPage() {
    const organization = useAtomValue(organizationState)
    const setRefreshTable = useSetAtom(refreshTableAtom)

    const handleCreateSuccess = () => {
        // 刷新表格数据
        setRefreshTable(prev => prev + 1)
    }
    return (
        <>
            <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200 py-2">
                <div className="flex flex-col gap-2 h-full">
                    <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                        {/* left */}
                        <div>
                            <h1 className="text-[18px]">密钥</h1>
                            <h2 className="text-[13px] text-zinc-500">
                                <span className="text-zinc-500 text-[13px]">
                                    管理API密钥、访问令牌和安全凭证
                                </span>
                            </h2>
                        </div>
                        {/* right */}
                        <div className="pr-4">
                            <CommonButton onClick={() => pushModal("CreateSecretModal", {
                                isOpen: true,
                                onClose: () => { },
                                onSuccess: handleCreateSuccess
                            })}>
                                <PlusIcon className="w-4 h-4" />
                                新建密钥
                            </CommonButton>
                        </div>
                    </header>
                    {/* <Separator /> */}
                    <DataTable
                        columns={secretColumn}
                        isNeedSelect={true}
                        onFetch={async (params) => secretApi.pageSecret(organization?.organizationId, params)}
                        dependencies={[organization]}
                        toolbar={(table, tableId) => {
                            return <DataTableToolbar table={table} tableId={tableId}>
                                <ToolbarLeft>

                                </ToolbarLeft>
                                {
                                    table.getFilteredSelectedRowModel().rows.length > 0 && (
                                        <FloatingMultiActionBar
                                            selectedCount={table.getFilteredSelectedRowModel().rows.length}
                                            selectedItems={table.getFilteredSelectedRowModel().rows.map((row: any) => row.original)}
                                            actions={[{
                                                id: "delete",
                                                label: "删除",
                                                shortcut: "D",
                                                onClick: (selectedItems) => {
                                                    const itemsToDelete = [...selectedItems];
                                                    pushModal("DoubleCheckModal", {
                                                        title: "删除密钥",
                                                        description: "确定要删除这些密钥吗？",
                                                        onConfirm: async () => {

                                                            toast.promise(async () => {
                                                                await secretApi.deleteSecret(organization?.organizationId!!, itemsToDelete.map((item: any) => item.id))
                                                                setRefreshTable(prev => prev + 1)
                                                                table.resetRowSelection()
                                                            }, {
                                                                loading: "删除中...",
                                                                success: "删除密钥成功",
                                                                error: "删除密钥失败"
                                                            })
                                                        }
                                                    })
                                                }
                                            }]
                                            }
                                        />
                                    )
                                }
                            </DataTableToolbar>
                        }}
                    />
                </div>
            </div>
        </>
    )
}