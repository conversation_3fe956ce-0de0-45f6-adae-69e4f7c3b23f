import { useState, useEffect } from "react";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useRequest } from "ahooks";
import { toast } from "sonner";
import { Download, TrendingUp } from "lucide-react";

import CommonButton from "@/components/ui/button/new-button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/table/base-table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loading } from "@/components/ui/loading";

import { organizationState } from "@/state/user-state";
import { refreshTableAtom } from "@/state/table-state";
import { WalletApi } from "@/api/wallet/wallet-api";
import { usageDiv } from "@/lib/utils";
import { walletColumn } from "./wallet-column";
import { pushModal } from "@/components/modals";

export default function WalletPage() {
    const organization = useAtomValue(organizationState);
    const setRefreshTable = useSetAtom(refreshTableAtom);

    const [rechargeValue, setRechargeValue] = useState<string>("");

    // 获取余额
    const { data: balance, loading: balanceLoading, refresh: refreshBalance } = useRequest(
        () => organization ? WalletApi.getBalance(organization.organizationId) : Promise.resolve({
            balance: 0,
            minimumAmount: 0,
            rechargeAmount: 0
        }),
        {
            refreshDeps: [organization?.organizationId],
            ready: !!organization?.organizationId
        }
    );

    const [minimumAmount, setMinimumAmount] = useState<number>(0);
    const [rechargeAmount, setRechargeAmount] = useState<number>(0);

    // 更新自动充值设置
    const { runAsync: updateAutoWallet, loading: updateLoading } = useRequest(
        WalletApi.updateAutoWallet,
        {
            manual: true,
            onSuccess: () => {
                toast.success("自动充值设置更新成功");
            },
            onError: (error) => {
                toast.error("更新失败: " + error.message);
            }
        }
    );

    // 充值功能
    const { runAsync: addPayment, loading: paymentLoading } = useRequest(
        WalletApi.addPayment,
        {
            manual: true,
            onSuccess: () => {
                toast.success("充值请求成功");
                setRechargeValue("");
                refreshBalance();
                // 刷新交易记录数据
                setRefreshTable(prev => prev + 1);
            },
            onError: (error) => {
                toast.error("充值失败: " + error.message);
            }
        }
    );

    // 初始化设置值
    useEffect(() => {
        setMinimumAmount(balance?.minimumAmount || 0);
        setRechargeAmount(balance?.rechargeAmount || 0);
    }, [balance]);

    const handleSaveAutoWallet = async () => {
        if (!organization) return;

        await updateAutoWallet({
            organizationId: organization.organizationId,
            minimumAmount,
            rechargeAmount
        });
    };

    const handleRecharge = async () => {
        if (!organization || !rechargeValue) return;

        const amount = parseFloat(rechargeValue);
        if (isNaN(amount) || amount <= 0) {
            toast.error("请输入有效的充值金额");
            return;
        }

        await addPayment(organization.organizationId, amount);
    };

    const formatCurrency = (amount: number) => {
        return `$${(amount / usageDiv).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    const formatAmount = (amount: number) => {
        return (amount).toLocaleString('zh-CN', { minimumFractionDigits: 6, maximumFractionDigits: 6 });
    };

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-4 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    <div>
                        <h1 className="text-[18px]">钱包</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            管理钱包余额、交易记录和充值
                        </h2>
                    </div>
                    <div className="pr-4">
                        <CommonButton onClick={() => pushModal("WalletBillModal")}>
                            <Download className="w-4 h-4" />
                            导出账单
                        </CommonButton>
                    </div>
                </header>
                <Separator />

                <div className="px-4 pb-4 space-y-4 flex-1 overflow-auto">
                    {/* 余额和充值卡片 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* 当前余额卡片 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    当前余额
                                </CardTitle>
                                <CardDescription>
                                    您的账户可用余额
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {balanceLoading ? (
                                    <div className="h-8 flex items-center">
                                        <Loading className="min-h-[2rem]" />
                                    </div>
                                ) : (
                                    <div className="text-2xl font-bold text-green-600">
                                        {formatAmount(balance?.balance || 0)}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* 快速充值卡片 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    快速充值
                                </CardTitle>
                                <CardDescription>
                                    输入金额进行充值
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex gap-2">
                                    <Input
                                        placeholder="输入充值金额"
                                        value={rechargeValue}
                                        onChange={(e) => setRechargeValue(e.target.value)}
                                        type="number"
                                        step="1"
                                        min="0"
                                    />
                                    <CommonButton
                                        className="!w-28"
                                        onClick={handleRecharge}
                                        disabled={!rechargeValue || paymentLoading}
                                        loading={paymentLoading}
                                        size="xs"
                                    >
                                        {paymentLoading ? "充值中..." : "充值"}
                                    </CommonButton>
                                </div>
                                <div className="flex gap-2">
                                    {[100, 500, 1000, 2000].map((amount) => (
                                        <Button
                                            key={amount}
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setRechargeValue(amount.toString())}
                                            className="text-xs"
                                        >
                                            {formatCurrency(amount * usageDiv)}
                                        </Button>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                自动充值设置
                            </CardTitle>
                            <CardDescription>
                                当余额不足时自动充值
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="minimumAmount">最低余额阈值 (元)</Label>
                                    <Input
                                        id="minimumAmount"
                                        type="number"
                                        value={minimumAmount}
                                        onChange={(e) => setMinimumAmount(Number(e.target.value))}
                                        placeholder="最低余额"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="rechargeAmount">自动充值金额 (元)</Label>
                                    <Input
                                        id="rechargeAmount"
                                        type="number"
                                        value={rechargeAmount}
                                        onChange={(e) => setRechargeAmount(Number(e.target.value))}
                                        placeholder="充值金额"
                                    />
                                </div>
                            </div>
                            <CommonButton
                                variant="outline"
                                onClick={handleSaveAutoWallet}
                                disabled={updateLoading}
                                className="w-full md:w-auto"
                                size="xs"
                            >
                                {updateLoading ? "保存中..." : "保存设置"}
                            </CommonButton>
                        </CardContent>
                    </Card>

                    {/* 交易记录表格 */}
                    <Card className="flex-1">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="w-5 h-5 text-purple-600" />
                                交易记录
                            </CardTitle>
                            <CardDescription>
                                您的账户变动记录
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-0">
                            <DataTable
                                columns={walletColumn}
                                dependencies={[organization?.organizationId]}
                                onFetch={async (params) => {
                                    if (!organization) {
                                        return { content: [], total: 0 };
                                    }

                                    try {
                                        const records = await WalletApi.getOrganizationWalletRecord(organization.organizationId);

                                        // 简单的客户端分页和排序
                                        let sortedRecords = [...records];

                                        // 排序
                                        if (params.sort && params.sort.length > 0) {
                                            const sortField = params.sort[0];
                                            const sortDirection = params.direction?.[0] || 'DESC';

                                            sortedRecords.sort((a, b) => {
                                                let aValue: any = (a as any)[sortField];
                                                let bValue: any = (b as any)[sortField];

                                                if (sortField === 'amountOfMoney') {
                                                    aValue = Number(aValue);
                                                    bValue = Number(bValue);
                                                } else if (sortField === 'createTime') {
                                                    aValue = Number(aValue);
                                                    bValue = Number(bValue);
                                                }

                                                if (typeof aValue === 'number' && typeof bValue === 'number') {
                                                    return sortDirection === 'ASC' ? aValue - bValue : bValue - aValue;
                                                }

                                                const aStr = String(aValue || '').toLowerCase();
                                                const bStr = String(bValue || '').toLowerCase();
                                                const result = aStr.localeCompare(bStr);
                                                return sortDirection === 'ASC' ? result : -result;
                                            });
                                        } else {
                                            // 默认按创建时间倒序
                                            sortedRecords.sort((a, b) => Number(b.createTime) - Number(a.createTime));
                                        }

                                        // 分页
                                        const start = params.pagination.pageIndex * params.pagination.pageSize;
                                        const end = start + params.pagination.pageSize;
                                        const paginatedRecords = sortedRecords.slice(start, end);

                                        return {
                                            content: paginatedRecords,
                                            total: records.length
                                        };
                                    } catch (error) {
                                        console.error("获取交易记录失败:", error);
                                        return { content: [], total: 0 };
                                    }
                                }}
                                isFixedHeader={false}
                                containerHeight="400px"
                            />
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}