import type { OrganizationWalletRecord } from "@/api/wallet/wallet-model";
import { Badge } from "@/components/ui/badge";
import { cn, usageDiv } from "@/lib/utils";
import type { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";

export const walletColumn: ColumnDef<OrganizationWalletRecord>[] = [
    {
        id: "createTime",
        header: "交易时间",
        accessorKey: "createTime",
        enableSorting: true,
        meta: {
            enableSortingIcon: true,
        },
        cell: ({ row }) => {
            return (
                <div className="text-sm">
                    {dayjs(Number(row.original.createTime)).format("YYYY-MM-DD HH:mm:ss")}
                </div>
            );
        }
    },
    {
        id: "amountOfMoneyType",
        header: "交易类型",
        accessorKey: "amountOfMoneyType",
        cell: ({ row }) => {
            const type = row.original.amountOfMoneyType;
            const typeConfig = {
                0: { label: "赠送", color: "bg-blue-100 text-blue-800" },
                1: { label: "充值", color: "bg-green-100 text-green-800" },
                2: { label: "退款", color: "bg-orange-100 text-orange-800" }
            };

            const config = typeConfig[type as keyof typeof typeConfig] || {
                label: "未知",
                color: "bg-gray-100 text-gray-800"
            };

            return (
                <Badge variant="outline" className={cn("gap-1.5", config.color)}>
                    <span
                        className={cn("size-1.5 rounded-full",
                            type === 0 ? "bg-blue-500" :
                                type === 1 ? "bg-green-500" :
                                    type === 2 ? "bg-orange-500" : "bg-gray-500"
                        )}
                        aria-hidden="true"
                    />
                    {config.label}
                </Badge>
            );
        }
    },
    {
        id: "amountOfMoney",
        header: () => <div className="text-right">金额</div>,
        accessorKey: "amountOfMoney",
        enableSorting: true,
        meta: {
            enableSortingIcon: true,
        },
        cell: ({ row }) => {
            const amount = Number(row.original.amountOfMoney);
            const type = row.original.amountOfMoneyType;
            const isPositive = type === 0 || type === 1; // 赠送和充值为正数

            return (
                <div className={cn(
                    "text-right font-mono",
                    isPositive ? "text-green-600" : "text-red-600"
                )}>
                    {isPositive ? "+" : "-"}${Math.abs(amount / usageDiv).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}
                </div>
            );
        }
    },
    {
        id: "statuses",
        header: "状态",
        accessorKey: "statuses",
        cell: ({ row }) => {
            const status = row.original.statuses;
            return (
                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                    <span
                        className={cn("size-1.5 rounded-full", status === 1 ? "bg-green-500" : "bg-red-500")}
                        aria-hidden="true"
                    />
                    {status === 1 ? "成功" : "失败"}
                </Badge>
            );
        }
    },
    {
        id: "id",
        header: "交易ID",
        accessorKey: "id",
        cell: ({ row }) => {
            return (
                <div className="font-mono text-xs text-gray-500">
                    {row.original.id.substring(0, 8)}...
                </div>
            );
        }
    }
]; 