-- 创建Channel同步状态表
-- 用于记录Google Vertex AI密钥同步的状态和进度

CREATE TABLE IF NOT EXISTS channel_sync_status (
    id BIGSERIAL PRIMARY KEY,
    sync_type VARCHAR(100) NOT NULL UNIQUE,
    last_sync_id BIGINT NOT NULL DEFAULT 0,
    last_sync_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sync_status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    error_message TEXT,
    sync_count INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_channel_sync_status_sync_type ON channel_sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_channel_sync_status_last_sync_time ON channel_sync_status(last_sync_time);

-- 插入初始记录
INSERT INTO channel_sync_status (sync_type, last_sync_id, sync_status) 
VALUES ('google_vertex_ai', 0, 'SUCCESS')
ON CONFLICT (sync_type) DO NOTHING;

-- 添加注释
COMMENT ON TABLE channel_sync_status IS 'Channel同步状态表，记录Google Vertex AI密钥同步的状态和进度';
COMMENT ON COLUMN channel_sync_status.sync_type IS '同步类型标识，如google_vertex_ai';
COMMENT ON COLUMN channel_sync_status.last_sync_id IS '上次同步的最大ID';
COMMENT ON COLUMN channel_sync_status.last_sync_time IS '上次同步时间';
COMMENT ON COLUMN channel_sync_status.sync_status IS '同步状态：SUCCESS, FAILED, RUNNING';
COMMENT ON COLUMN channel_sync_status.error_message IS '错误信息';
COMMENT ON COLUMN channel_sync_status.sync_count IS '本次同步的记录数';
