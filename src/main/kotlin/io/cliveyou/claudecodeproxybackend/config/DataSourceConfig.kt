package io.cliveyou.claudecodeproxybackend.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.sql.DataSource

/**
 * 双数据源配置
 * 主数据源：PostgreSQL（用于应用主要业务数据）
 * 辅助数据源：MySQL（用于同步GCP channels表数据）
 */
@Configuration
@EnableTransactionManagement
class DataSourceConfig {

    /**
     * 主数据源配置 - PostgreSQL
     */
    @Configuration
    @EnableJpaRepositories(
        basePackages = ["io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository"],
        entityManagerFactoryRef = "entityManagerFactory",
        transactionManagerRef = "transactionManager"
    )
    class PrimaryDataSourceConfig {

        @Primary
        @Bean(name = ["dataSource"])
        @ConfigurationProperties(prefix = "spring.datasource")
        fun dataSource(): DataSource {
            return DataSourceBuilder.create().build()
        }

        @Primary
        @Bean(name = ["entityManagerFactory"])
        fun entityManagerFactory(
            @Qualifier("dataSource") dataSource: DataSource
        ): LocalContainerEntityManagerFactoryBean {
            val em = LocalContainerEntityManagerFactoryBean()
            em.dataSource = dataSource
            em.setPackagesToScan("io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity")

            val vendorAdapter = HibernateJpaVendorAdapter()
            em.jpaVendorAdapter = vendorAdapter

            return em
        }

        @Primary
        @Bean(name = ["transactionManager"])
        fun transactionManager(
            @Qualifier("entityManagerFactory") entityManagerFactory: LocalContainerEntityManagerFactoryBean
        ): PlatformTransactionManager {
            return JpaTransactionManager(entityManagerFactory.`object`!!)
        }
    }

    /**
     * 辅助数据源配置 - MySQL
     */
    @Configuration
    @EnableJpaRepositories(
        basePackages = ["io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository"],
        entityManagerFactoryRef = "mysqlEntityManagerFactory",
        transactionManagerRef = "mysqlTransactionManager"
    )
    class SecondaryDataSourceConfig {

        @Bean(name = ["mysqlDataSource"])
        @ConfigurationProperties(prefix = "spring.datasource.mysql")
        fun mysqlDataSource(): DataSource {
            return DataSourceBuilder.create().build()
        }

        @Bean(name = ["mysqlEntityManagerFactory"])
        fun mysqlEntityManagerFactory(
            @Qualifier("mysqlDataSource") dataSource: DataSource
        ): LocalContainerEntityManagerFactoryBean {
            val em = LocalContainerEntityManagerFactoryBean()
            em.dataSource = dataSource
            em.setPackagesToScan("io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity")

            val vendorAdapter = HibernateJpaVendorAdapter()
            em.jpaVendorAdapter = vendorAdapter

            val properties = HashMap<String, Any>()
            properties["hibernate.hbm2ddl.auto"] = "none"
            properties["hibernate.dialect"] = "org.hibernate.dialect.MySQLDialect"
            properties["hibernate.show_sql"] = false
            em.setJpaPropertyMap(properties)

            return em
        }

        @Bean(name = ["mysqlTransactionManager"])
        fun mysqlTransactionManager(
            @Qualifier("mysqlEntityManagerFactory") entityManagerFactory: LocalContainerEntityManagerFactoryBean
        ): PlatformTransactionManager {
            return JpaTransactionManager(entityManagerFactory.`object`!!)
        }
    }
}