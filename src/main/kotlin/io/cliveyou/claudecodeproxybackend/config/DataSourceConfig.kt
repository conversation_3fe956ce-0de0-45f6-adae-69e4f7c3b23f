package io.cliveyou.claudecodeproxybackend.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.transaction.PlatformTransactionManager
import javax.sql.DataSource

/**
 * MySQL数据源配置
 * 用于连接GCP MySQL数据库同步channels表数据
 * PostgreSQL作为主数据源由Spring Boot自动配置
 */
// @Configuration
// @EnableJpaRepositories(
//     basePackages = ["io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository"],
//     entityManagerFactoryRef = "mysqlEntityManagerFactory",
//     transactionManagerRef = "mysqlTransactionManager"
// )
class DataSourceConfig {

    // @Bean(name = ["mysqlDataSource"])
    // @ConfigurationProperties(prefix = "spring.datasource.mysql")
    // fun mysqlDataSource(): DataSource {
    //     return DataSourceBuilder.create().build()
    // }

    // @Bean(name = ["mysqlEntityManagerFactory"])
    // fun mysqlEntityManagerFactory(
    //     @Qualifier("mysqlDataSource") dataSource: DataSource
    // ): LocalContainerEntityManagerFactoryBean {
    //     val em = LocalContainerEntityManagerFactoryBean()
    //     em.dataSource = dataSource
    //     em.setPackagesToScan("io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity")

    //     val vendorAdapter = HibernateJpaVendorAdapter()
    //     em.jpaVendorAdapter = vendorAdapter

    //     val properties = HashMap<String, Any>()
    //     properties["hibernate.hbm2ddl.auto"] = "none"
    //     properties["hibernate.dialect"] = "org.hibernate.dialect.MySQLDialect"
    //     properties["hibernate.show_sql"] = false
    //     em.setJpaPropertyMap(properties)

    //     return em
    // }

    // @Bean(name = ["mysqlTransactionManager"])
    // fun mysqlTransactionManager(
    //     @Qualifier("mysqlEntityManagerFactory") entityManagerFactory: LocalContainerEntityManagerFactoryBean
    // ): PlatformTransactionManager {
    //     return JpaTransactionManager(entityManagerFactory.`object`!!)
    // }
}