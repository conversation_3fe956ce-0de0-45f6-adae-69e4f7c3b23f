package io.cliveyou.claudecodeproxybackend.sync.application.service

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.ChannelSyncStatusRepository
import io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository.ChannelRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

/**
 * Channel同步测试服务
 * 用于测试数据源配置是否正确
 */
@Service
class ChannelSyncTestService(
    private val channelRepository: ChannelRepository,
    private val channelSyncStatusRepository: ChannelSyncStatusRepository
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 测试数据源连接
     */
    fun testDataSourceConnections(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()
        
        try {
            // 测试MySQL连接（Channel表）
            val channelCount = channelRepository.count()
            result["mysql_connection"] = "SUCCESS"
            result["mysql_channel_count"] = channelCount
            log.info { "MySQL connection test successful. Channel count: $channelCount" }
        } catch (e: Exception) {
            result["mysql_connection"] = "FAILED"
            result["mysql_error"] = e.message
            log.error(e) { "MySQL connection test failed: ${e.message}" }
        }
        
        try {
            // 测试PostgreSQL连接（ChannelSyncStatus表）
            val syncStatusCount = channelSyncStatusRepository.count()
            result["postgresql_connection"] = "SUCCESS"
            result["postgresql_sync_status_count"] = syncStatusCount
            log.info { "PostgreSQL connection test successful. Sync status count: $syncStatusCount" }
        } catch (e: Exception) {
            result["postgresql_connection"] = "FAILED"
            result["postgresql_error"] = e.message
            log.error(e) { "PostgreSQL connection test failed: ${e.message}" }
        }
        
        return result
    }
}
