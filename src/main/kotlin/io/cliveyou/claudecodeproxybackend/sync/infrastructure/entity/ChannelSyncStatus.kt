package io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * Channel同步状态实体类
 * 记录上次同步的ID，避免重复同步
 */
@Entity
@Table(name = "channel_sync_status")
data class ChannelSyncStatus(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 同步类型标识 (例如: "google_vertex_ai")
     */
    @Column(name = "sync_type", length = 100, nullable = false)
    val syncType: String,

    /**
     * 上次同步的最大ID
     */
    @Column(name = "last_sync_id", nullable = false)
    val lastSyncId: Long,

    /**
     * 上次同步时间
     */
    @Column(name = "last_sync_time", nullable = false)
    val lastSyncTime: LocalDateTime = LocalDateTime.now(),

    /**
     * 同步状态 (SUCCESS, FAILED, RUNNING)
     */
    @Column(name = "sync_status", length = 20, nullable = false)
    val syncStatus: String = "SUCCESS",

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    val errorMessage: String? = null,

    /**
     * 本次同步的记录数
     */
    @Column(name = "sync_count")
    val syncCount: Int? = 0
)
