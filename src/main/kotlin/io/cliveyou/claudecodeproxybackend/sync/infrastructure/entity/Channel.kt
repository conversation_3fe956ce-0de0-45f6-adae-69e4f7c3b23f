package io.cliveyou.claudecodeproxybackend.sync.infrastructure.entity

import jakarta.persistence.*

/**
 * Channel实体类
 * 对应GCP MySQL数据库中的channels表
 */
@Entity
@Table(name = "channels")
data class Channel(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 类型
     */
    @Column(name = "type")
    val type: Long? = 0,

    /**
     * 密钥内容 - 存储Google Vertex AI JSON数据
     */
    @Column(name = "`key`", columnDefinition = "LONGTEXT")
    val key: String,

    /**
     * OpenAI组织
     */
    @Column(name = "open_ai_organization", columnDefinition = "LONGTEXT")
    val openAiOrganization: String? = null,

    /**
     * 测试模型
     */
    @Column(name = "test_model", columnDefinition = "LONGTEXT")
    val testModel: String? = null,

    /**
     * 状态 (1=启用, 0=禁用)
     */
    @Column(name = "status")
    val status: Long? = 1,

    /**
     * 名称
     */
    @Column(name = "name", length = 191)
    val name: String? = null,

    /**
     * 权重
     */
    @Column(name = "weight")
    val weight: Long? = 0,

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    val createdTime: Long? = null,

    /**
     * 测试时间
     */
    @Column(name = "test_time")
    val testTime: Long? = null,

    /**
     * 响应时间
     */
    @Column(name = "response_time")
    val responseTime: Long? = null,

    /**
     * 基础URL
     */
    @Column(name = "base_url", length = 191)
    val baseUrl: String? = "",

    /**
     * 其他信息
     */
    @Column(name = "other", columnDefinition = "LONGTEXT")
    val other: String? = null,

    /**
     * 余额
     */
    @Column(name = "balance")
    val balance: Double? = null,

    /**
     * 余额更新时间
     */
    @Column(name = "balance_updated_time")
    val balanceUpdatedTime: Long? = null,

    /**
     * 模型列表
     */
    @Column(name = "models", columnDefinition = "LONGTEXT")
    val models: String? = null,

    /**
     * 分组
     */
    @Column(name = "`group`", length = 64)
    val group: String? = "default",

    /**
     * 已使用配额
     */
    @Column(name = "used_quota")
    val usedQuota: Long? = 0,

    /**
     * 模型映射
     */
    @Column(name = "model_mapping", columnDefinition = "TEXT")
    val modelMapping: String? = null,

    /**
     * 状态码映射
     */
    @Column(name = "status_code_mapping", length = 1024)
    val statusCodeMapping: String? = "",

    /**
     * 优先级
     */
    @Column(name = "priority")
    val priority: Long? = 0,

    /**
     * 自动禁用
     */
    @Column(name = "auto_ban")
    val autoBan: Long? = 1,

    /**
     * 其他信息
     */
    @Column(name = "other_info", columnDefinition = "LONGTEXT")
    val otherInfo: String? = null,

    /**
     * 设置
     */
    @Column(name = "settings", columnDefinition = "LONGTEXT")
    val settings: String? = null,

    /**
     * 标签
     */
    @Column(name = "tag", length = 191)
    val tag: String? = null,

    /**
     * 设置
     */
    @Column(name = "setting", columnDefinition = "TEXT")
    val setting: String? = null,

    /**
     * 参数覆盖
     */
    @Column(name = "param_override", columnDefinition = "TEXT")
    val paramOverride: String? = null
)
