package io.cliveyou.claudecodeproxybackend

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ThreadLocalRandom
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference

/** 配置参数，可热更新 */
data class SchedulerConfig(
    @Volatile var windowSize: Int = 100,       // N：滑窗容量
    @Volatile var epsilon: Double = 0.05,      // 探索率 ε
    @Volatile var errThreshold: Double = 0.5,  // 过滤阈值
    @Volatile var minSampleSize: Int = 15,      // 最小样本数，低于此数量不应用错误率过滤
)

/** 单个 Key 的运行时统计 */
class KeyStats(
    initialWeight: Double,
    private val windowSize: Int,                // 固定窗口大小
) {
    // 热更新权重
    @Volatile
    var weight: Double = initialWeight

    // 环形缓冲记录最近 N 次结果 (1=fail,0=success)
    private val results = IntArray(windowSize)
    private val index = AtomicInteger(0)
    private val count = AtomicInteger(0)       // 有效样本数量
    private val failSum = AtomicInteger(0)     // 窗口内失败数

    /** 记录一次调用结果 */
    fun record(success: Boolean) {
        val pos = index.getAndIncrement() % windowSize
        synchronized(this) {
            // 回收旧值
            if (count.get() >= windowSize) {
                if (results[pos] == 1) failSum.decrementAndGet()
            } else {
                count.incrementAndGet()
            }
            // 写新值
            results[pos] = if (success) 0 else 1
            if (!success) failSum.incrementAndGet()
        }
    }

    /** 当前错误率 */
    val errorRate: Double
        get() = if (count.get() == 0) 0.0 else failSum.get().toDouble() / count.get()

    /** 是否应该应用错误率过滤（样本数足够时才应用） */
    fun shouldApplyErrorRateFilter(minSampleSize: Int): Boolean {
        return count.get() >= minSampleSize
    }

    /** 复合得分 Score = Weight × (1 - ErrorRate) */
    val score: Double
        get() = weight * (1.0 - errorRate)

    /** 获取总请求数 */
    val totalRequests: Int
        get() = count.get()

    /** 获取成功请求数 */
    val successRequests: Int
        get() = count.get() - failSum.get()
}

/** 调度器主体 */
class KeyScheduler(
    config: SchedulerConfig = SchedulerConfig(),
) {
    private val cfgRef = AtomicReference(config)
    val statsMap = ConcurrentHashMap<String, KeyStats>()

    /** 注册或更新 Key 权重（热更新） */
    fun upsertKey(key: String, weight: Double) {
        val cfg = cfgRef.get()
        statsMap.compute(key) { _, s ->
            val stat = s ?: KeyStats(weight, cfg.windowSize)
            stat.weight = weight
            stat
        }
    }

    /** 在可用 Key 中按 ε‑Greedy 返回一个 Key；无可用时返回 null */
    fun chooseKey(): String? {
        val cfg = cfgRef.get()
        val eligible = statsMap.entries
            .filter { entry ->
                // 只有样本数足够时才应用错误率过滤，否则给新key试错机会
                !entry.value.shouldApplyErrorRateFilter(cfg.minSampleSize) ||
                entry.value.errorRate < cfg.errThreshold
            }
            .takeIf { it.isNotEmpty() } ?: return null

        val rand = ThreadLocalRandom.current().nextDouble()
        return if (rand < cfg.epsilon) {
            // 探索：随机选
            eligible.random().key
        } else {
            // 利用：选 Score 最大，如果多个相等则随机选
            val maxScore = eligible.maxOf { it.value.score }
            val maxScoreEntries = eligible.filter { it.value.score == maxScore }
            maxScoreEntries.random().key
        }
    }

    /** 从指定的候选 Key 中按 ε‑Greedy 返回一个 Key；无可用时返回 null */
    fun chooseKeyFromCandidates(candidateKeys: Set<String>): String? {
        val cfg = cfgRef.get()
        val eligible = statsMap.entries
            .filter { entry ->
                entry.key in candidateKeys && (
                    // 只有样本数足够时才应用错误率过滤，否则给新key试错机会
                    !entry.value.shouldApplyErrorRateFilter(cfg.minSampleSize) ||
                    entry.value.errorRate < cfg.errThreshold
                )
            }
            .takeIf { it.isNotEmpty() } ?: return null

        val rand = ThreadLocalRandom.current().nextDouble()
        return if (rand < cfg.epsilon) {
            // 探索：随机选
            eligible.random().key
        } else {
            // 利用：选 Score 最大，如果多个相等则随机选
            val maxScore = eligible.maxOf { it.value.score }
            val maxScoreEntries = eligible.filter { it.value.score == maxScore }
            maxScoreEntries.random().key
        }
    }

    /** 调用结束后汇报结果 */
    fun recordResult(key: String, success: Boolean) {
        statsMap[key]?.record(success)
    }

    /** 移除指定的Key */
    fun removeKey(key: String) {
        statsMap.remove(key)
    }

    /** 动态更新全局配置（窗口大小不可在线改，需重建 stats） */
    fun updateConfig(mutator: SchedulerConfig.() -> Unit) {
        val cfg = cfgRef.get()
        val newCfg = cfg.copy().apply(mutator)
        cfgRef.set(newCfg)
        // Note: 若 windowSize 改变，需重建 KeyStats，这里忽略
    }

    /** 获取当前配置 */
    fun getConfig(): SchedulerConfig {
        return cfgRef.get()
    }
}

/** Demo: 随机失败概率不同 */
fun simulateRpc(key: String): Boolean = when (key) {
    "keyA" -> ThreadLocalRandom.current().nextDouble() > 0.1  // 10% 失败
    "keyB" -> ThreadLocalRandom.current().nextDouble() > 0.3  // 30% 失败
    "keyC" -> ThreadLocalRandom.current().nextDouble() > 0.05 // 5% 失败
    else -> true
}
