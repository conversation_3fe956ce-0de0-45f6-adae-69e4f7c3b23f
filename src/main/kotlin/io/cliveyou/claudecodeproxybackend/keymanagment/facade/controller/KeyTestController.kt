package io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.keymanagment.application.service.KeyTestService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.test.ApiTestResult
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@CrossOrigin
@RestController
@RequestMapping("/api/key-test")
class KeyTestController(
    private val keyTestService: KeyTestService,
) {


    @RequestMapping("/{channel}/{id}")
    fun keyTest(
        @PathVariable id: Long,
        @PathVariable channel: String,
        @RequestParam("model", required = true) model: String,
    ): Mono<PlatformResult<ApiTestResult>> {
        log.info { "Received request to test key: $id and channel: $channel" }
        return keyTestService.testKey(channel, id, model).map { PlatformResult.success(it) }
    }

    companion object {
        private val log = KotlinLogging.logger { }
    }
}