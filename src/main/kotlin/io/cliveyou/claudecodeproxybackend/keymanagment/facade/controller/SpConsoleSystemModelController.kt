package io.cliveyou.claudecodeproxybackend.keymanagment.facade.controller

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.SpConsoleSystemModelResponse
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.toResponse
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.SpConsoleSystemModelRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * 系统模型控制器
 */
@CrossOrigin
@RestController
@RequestMapping("/api/private/systemModel")
class SpConsoleSystemModelController(
    private val systemModelRepository: SpConsoleSystemModelRepository
) {

    private val log = KotlinLogging.logger {}

    /**
     * 查询所有模型信息
     */
    @GetMapping("/all")
    fun getAllModels(): PlatformResult<List<SpConsoleSystemModelResponse>> {
        val models = systemModelRepository.findAll()
        val response = models.map { it.toResponse() }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 查询所有显示的模型
     */
    @GetMapping("/display")
    fun getDisplayModels(): PlatformResult<List<SpConsoleSystemModelResponse>> {
        log.info { "Received request to get display system models" }

        val models = systemModelRepository.findByStatuses(1)
        val response = models.map { it.toResponse() }

        log.info { "Found ${response.size} display system models" }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 查询所有有效的模型（显示且未消失）
     */
    @GetMapping("/active")
    fun getActiveModels(): PlatformResult<List<SpConsoleSystemModelResponse>> {
        log.info { "Received request to get active system models" }

        val currentTime = System.currentTimeMillis() / 1000 // 转换为秒
        val models = systemModelRepository.findAllActiveModels(currentTime)
        val response = models.map { it.toResponse() }

        log.info { "Found ${response.size} active system models" }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 根据厂商名称查询模型
     */
    @GetMapping("/manufacturer/{manufacturerName}")
    fun getModelsByManufacturer(
        @PathVariable manufacturerName: String
    ): PlatformResult<List<SpConsoleSystemModelResponse>> {
        log.info { "Received request to get models by manufacturer: $manufacturerName" }

        val models = systemModelRepository.findByManufacturerNameContainingIgnoreCase(manufacturerName)
        val response = models.map { it.toResponse() }

        log.info { "Found ${response.size} models for manufacturer: $manufacturerName" }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 根据模型类型查询
     */
    @GetMapping("/type/{modelType}")
    fun getModelsByType(
        @PathVariable modelType: Int
    ): PlatformResult<List<SpConsoleSystemModelResponse>> {
        log.info { "Received request to get models by type: $modelType" }

        val models = systemModelRepository.findByModelType(modelType)
        val response = models.map { it.toResponse() }

        log.info { "Found ${response.size} models for type: $modelType" }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 根据状态查询
     */
    @GetMapping("/status/{statuses}")
    fun getModelsByStatus(
        @PathVariable statuses: Int
    ): PlatformResult<List<SpConsoleSystemModelResponse>> {
        log.info { "Received request to get models by status: $statuses" }

        val models = systemModelRepository.findByStatuses(statuses)
        val response = models.map { it.toResponse() }

        log.info { "Found ${response.size} models for status: $statuses" }
        return PlatformResult.Companion.success(response)
    }

    /**
     * 根据ID查询单个模型
     */
    @GetMapping("/{id}")
    fun getModelById(
        @PathVariable id: Long
    ): PlatformResult<SpConsoleSystemModelResponse> {
        log.info { "Received request to get model by id: $id" }

        val model = systemModelRepository.findById(id)

        return if (model.isPresent) {
            val response = model.get().toResponse()
            log.info { "Found model with id: $id" }
            PlatformResult.Companion.success(response)
        } else {
            log.warn { "Model not found with id: $id" }
            PlatformResult.Companion.notFound("模型不存在")
        }
    }
}