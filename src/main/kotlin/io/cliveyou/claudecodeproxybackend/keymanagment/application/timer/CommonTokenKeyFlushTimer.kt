package io.cliveyou.claudecodeproxybackend.keymanagment.application.timer

import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.TokenManagementService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ClaudeType
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.ClaudeCodeFlushService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.GoogleVertexAIFlushService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.runBlocking
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/**
 * 通用密钥刷新定时任务（支持 Claude Code 和 Google Vertex AI）
 */
@Component
class CommonTokenKeyFlushTimer(
    private val tokenManagementService: TokenManagementService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository,
    private val claudeCodeFlushService: ClaudeCodeFlushService,
    private val googleVertexAIFlushService: GoogleVertexAIFlushService,
    private val keyManageService: KeyManageService,
) {

    /**
     * 定时扫描即将过期的 Claude Code 和 Google Vertex AI key 并刷新
     * 每10分钟执行一次
     */
//    @Scheduled(fixedRate = 600000) // 10分钟 = 600,000毫秒
    fun refreshExpiringKeys() {
        runBlocking {
            try {
                log.info { "Refresh Timer | Scheduled refresh started | Info: Scanning for expiring Claude Code and Google Vertex AI keys" }

                val fivMinutesLater = System.currentTimeMillis() / 1000 + 15 * 60

                // 查找30分钟内即将过期的 Google Vertex AI 密钥
                val expiringGoogleKeys = tokenManagementService.findGoogleVertexAIKeysNeedingRefresh(fivMinutesLater)

                if (expiringGoogleKeys.isEmpty()) {
                    log.info { "Refresh Timer | No expiring keys found | Info: All keys are valid for next 30 minutes" }
                    return@runBlocking
                }

                // 批量刷新 Google Vertex AI tokens
                val googleRefreshResult = googleVertexAIFlushService.refreshGoogleVertexAITokens(expiringGoogleKeys)
                processGoogleRefreshResults(googleRefreshResult)

                log.info { "Refresh Timer | Scheduled refresh completed | Success: All tokens processed" }

            } catch (e: Exception) {
                log.error(e) { "Refresh Timer | Scheduled refresh failed | Failed: ${e.message} | Suggestion: Check database connectivity and service availability" }
            }
        }
    }

    private suspend fun processClaudeRefreshResults(refreshResult: io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.TokenRefreshBatchResult) {
        // 保存刷新成功的key
        if (refreshResult.successfulResults.isNotEmpty()) {
            refreshResult.successfulResults.forEach { result ->
                tokenManagementService.refreshClaudeCodeAccessToken(
                    result.keyId,
                    result.accessToken,
                    result.expiresAt,
                    result.refreshToken
                )
            }
            log.info { "Refresh Timer | Claude Code database update completed | Success: ${refreshResult.successfulResults.size} keys saved to database" }

            // 更新内存中的key数据
            val tokenKeys = refreshResult.successfulResults.mapNotNull { result ->
                commonTokenKeyRepository.findById(result.keyId).orElse(null)
            }
            tokenKeys.forEach { tokenKey ->
                keyManageService.updateKey(tokenKey)
            }
            log.info { "Refresh Timer | Claude Code memory update completed | Success: ${tokenKeys.size} keys updated in memory" }
        }

        // 处理刷新失败的key
        if (refreshResult.failedPairs.isNotEmpty()) {
            log.warn { "Refresh Timer | Some Claude Code keys failed to refresh | Warning: ${refreshResult.failedPairs.size} keys failed | Suggestion: Check failed keys manually" }

            // 将失败的key标记为认证失败状态
            refreshResult.failedPairs.forEach { (tokenKey, _) ->
                val failedKeyId = tokenKey.id
                if (failedKeyId != null) {
                    val existingKey = commonTokenKeyRepository.findById(failedKeyId).orElse(null)
                    if (existingKey != null) {
                        val updatedKey = existingKey.copy(
                            status = CommonKeyStatus.AUTH_FAILED,
                            updatedAt = LocalDateTime.now()
                        )
                        commonTokenKeyRepository.save(updatedKey)
                    }
                }
            }
            log.info { "Refresh Timer | Failed Claude Code keys marked as AUTH_FAILED | Info: ${refreshResult.failedPairs.size} keys updated" }
        }
    }

    private suspend fun processGoogleRefreshResults(refreshResult: io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.GoogleVertexAITokenRefreshBatchResult) {
        // 保存刷新成功的key
        if (refreshResult.successfulResults.isNotEmpty()) {
            refreshResult.successfulResults.forEach { result ->
                tokenManagementService.refreshGoogleVertexAIAccessToken(
                    result.keyId,
                    result.accessToken,
                    result.expiresAt
                )
            }
            log.info { "Refresh Timer | Google Vertex AI database update completed | Success: ${refreshResult.successfulResults.size} keys saved to database" }

            // 更新内存中的key数据
            val tokenKeys = refreshResult.successfulResults.mapNotNull { result ->
                commonTokenKeyRepository.findById(result.keyId).orElse(null)
            }
            tokenKeys.forEach { tokenKey ->
                keyManageService.updateKey(tokenKey)
            }
            log.info { "Refresh Timer | Google Vertex AI memory update completed | Success: ${tokenKeys.size} keys updated in memory" }
        }

        // 处理刷新失败的key
        if (refreshResult.failedPairs.isNotEmpty()) {
            log.warn { "Refresh Timer | Some Google Vertex AI keys failed to refresh | Warning: ${refreshResult.failedPairs.size} keys failed | Suggestion: Check failed keys manually" }

            // 将失败的key标记为认证失败状态
            refreshResult.failedPairs.forEach { (tokenKey, _) ->
                val failedKeyId = tokenKey.id
                if (failedKeyId != null) {
                    val existingKey = commonTokenKeyRepository.findById(failedKeyId).orElse(null)
                    if (existingKey != null) {
                        val updatedKey = existingKey.copy(
                            status = CommonKeyStatus.AUTH_FAILED,
                            updatedAt = LocalDateTime.now()
                        )
                        commonTokenKeyRepository.save(updatedKey)
                    }
                }
            }
            log.info { "Refresh Timer | Failed Google Vertex AI keys marked as AUTH_FAILED | Info: ${refreshResult.failedPairs.size} keys updated" }
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}