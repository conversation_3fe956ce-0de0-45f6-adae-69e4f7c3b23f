package io.cliveyou.claudecodeproxybackend.keymanagment.application.service

import io.cliveyou.claudecodeproxybackend.common.exception.InvalidParameterException
import io.cliveyou.claudecodeproxybackend.common.exception.ResourceNotFoundException
import io.cliveyou.claudecodeproxybackend.common.exception.KeyManagementException
import io.cliveyou.claudecodeproxybackend.common.exception.TokenRefreshException
import io.cliveyou.claudecodeproxybackend.common.response.PageResponse
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyManageService
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.TokenManagementService
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.ClaudeCodeFlushService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.CommonTokenKeyRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.persistence.criteria.Predicate
import kotlinx.coroutines.runBlocking
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue

@Service
@Transactional
class CommonTokenKeyService(
    private val keyManageService: KeyManageService,
    private val tokenManagementService: TokenManagementService,
    private val commonTokenKeyRepository: CommonTokenKeyRepository,
    private val claudeCodeFlushService: ClaudeCodeFlushService,
    private val googleVertexAIFlushService: io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush.GoogleVertexAIFlushService,
    private val objectMapper: ObjectMapper
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 创建通用密钥
     */
    fun createKey(request: CreateCommonTokenKeyRequest): CommonTokenKeyResponse {
        log.info { "Creating ${request.type} key with name: ${request.name}" }
        
        val key = CommonTokenKey(
            accessToken = request.accessToken,
            domain = request.domain,
            type = request.type,
            status = CommonKeyStatus.ACTIVE,
            name = request.name,
            supportModels = request.supportModels,
            autoDisable = request.autoDisable,
            modelMapping = request.modelMapping,
            metadata = request.metadata,
            quota = request.quota,
            weight = request.weight.toBigDecimal(),
            windowSize = request.windowSize,
            epsilon = request.epsilon.toBigDecimal(),
            errThreshold = request.errThreshold.toBigDecimal(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
        
        val refreshInfo = if (request.type == KeyChannel.ClaudeCode) {
            val claudeRequest = request as? CreateClaudeCodeKeyRequest
            if (claudeRequest != null) {
                ClaudeCodeTokenRefreshInfo(
                    commonKeyId = 0, // 将在 tokenManagementService 中设置
                    refreshToken = claudeRequest.refreshToken,
                    expiresAt = claudeRequest.expiresAt,
                    clientId = claudeRequest.clientId,
                    email = claudeRequest.email,
                    accountType = claudeRequest.accountType,
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now()
                )
            } else {
                null
            }
        } else {
            null
        }
        
        val savedKey = tokenManagementService.createTokenKey(key, refreshInfo)

        // 如果密钥是活跃状态且有访问令牌，则更新内存中的密钥管理
        if (savedKey.status == CommonKeyStatus.ACTIVE && !savedKey.accessToken.isNullOrBlank()) {
            // 将密钥添加到其支持的每个模型中
            savedKey.supportModels.forEach { modelName ->
                keyManageService.addKeyToModel(modelName, savedKey)
            }
        }

        log.info { "Successfully created ${savedKey.type} key with ID: ${savedKey.id}" }
        return savedKey.toCommonResponse()
    }

    /**
     * 更新通用密钥
     */
    fun updateKey(request: UpdateCommonTokenKeyRequest): CommonTokenKeyResponse {
        log.info { "Updating key with ID: ${request.id}" }
        
        val existingKey = commonTokenKeyRepository.findById(request.id)
            .orElseThrow { IllegalArgumentException("密钥不存在，ID: ${request.id}") }
        
        val updates = mutableMapOf<String, Any>()
        request.accessToken?.let { updates["accessToken"] = it }
        request.domain?.let { updates["domain"] = it }
        request.status?.let { updates["status"] = it }
        request.name?.let { updates["name"] = it }
        request.supportModels?.let { updates["supportModels"] = it }
        request.autoDisable?.let { updates["autoDisable"] = it }
        request.modelMapping?.let { updates["modelMapping"] = it }
        request.metadata?.let { updates["metadata"] = it }
        request.quota?.let { updates["quota"] = it }
        request.weight?.let { updates["weight"] = it }
        request.windowSize?.let { updates["windowSize"] = it }
        request.epsilon?.let { updates["epsilon"] = it }
        request.errThreshold?.let { updates["errThreshold"] = it }
        
        val refreshInfoUpdates = if (existingKey.type == KeyChannel.ClaudeCode) {
            val claudeRequest = request as? UpdateClaudeCodeKeyRequest
            if (claudeRequest != null) {
            mutableMapOf<String, Any>().apply {
                    claudeRequest.refreshToken?.let { put("refreshToken", it) }
                    claudeRequest.expiresAt?.let { put("expiresAt", it) }
                    claudeRequest.clientId?.let { put("clientId", it) }
                    claudeRequest.email?.let { put("email", it) }
                    claudeRequest.accountType?.let { put("accountType", it) }
                }
            } else {
                null
            }
        } else {
            null
        }
        
        val savedKey = tokenManagementService.updateTokenKey(request.id, updates, refreshInfoUpdates)

        // 更新内存中的密钥管理
        if (savedKey.status == CommonKeyStatus.ACTIVE && !savedKey.accessToken.isNullOrBlank()) {
            // 先移除旧的密钥映射
            keyManageService.removeKey(savedKey)
            // 重新添加到支持的模型中
            savedKey.supportModels.forEach { modelName ->
                keyManageService.addKeyToModel(modelName, savedKey)
            }
        } else if (!savedKey.accessToken.isNullOrBlank()) {
            // 如果密钥被禁用，从内存中移除
            keyManageService.removeKey(savedKey)
        }
        
        log.info { "Successfully updated key with ID: ${savedKey.id}" }
        return savedKey.toCommonResponse()
    }

    /**
     * 删除密钥
     */
    fun deleteKey(id: Long) {
        log.info { "Deleting key with ID: $id" }
        
        val key = commonTokenKeyRepository.findById(id)
            .orElseThrow { IllegalArgumentException("密钥不存在，ID: $id") }
        
        // 从内存中移除密钥
        if (!key.accessToken.isNullOrBlank()) {
            keyManageService.removeKey(key)
        }
        
        tokenManagementService.deleteTokenKey(id)
        log.info { "Successfully deleted key with ID: $id" }
    }

    /**
     * 批量删除密钥
     */
    fun deleteKeys(ids: List<Long>) {
        log.info { "Batch deleting keys with IDs: $ids" }
        
        val keys = commonTokenKeyRepository.findAllById(ids)
        
        // 从内存中移除密钥
        keys.forEach { key ->
            if (!key.accessToken.isNullOrBlank()) {
                keyManageService.removeKey(key)
            }
        }
        
        ids.forEach { id ->
            tokenManagementService.deleteTokenKey(id)
        }
        
        log.info { "Successfully batch deleted ${keys.size} keys" }
    }

    /**
     * 根据ID查询密钥
     */
    fun getKeyById(id: Long): CommonTokenKeyResponse {
        val key = commonTokenKeyRepository.findById(id)
            .orElseThrow { IllegalArgumentException("密钥不存在，ID: $id") }
        
        val response = key.toCommonResponse()
        
        // 如果是 Claude Code 类型，添加刷新信息
        if (key.type == KeyChannel.ClaudeCode) {
            val (_, refreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(id)
            response.claudeCodeRefreshInfo = refreshInfo?.toResponse()
        }
        
        // 如果是 Google Vertex AI 类型，添加刷新信息
        if (key.type == KeyChannel.GoogleVertexAI) {
            val (_, refreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(id)
            response.googleVertexAIRefreshInfo = refreshInfo?.toResponse()
        }
        
        return response
    }

    /**
     * 分页查询密钥
     */
    fun pageKeys(request: PageCommonTokenKeyRequest): PageResponse<CommonTokenKeyResponse> {
        val spec = Specification<CommonTokenKey> { root, _, criteriaBuilder ->
            val predicates = mutableListOf<Predicate>()
            
            // 按名称模糊查询
            request.name?.takeIf { it.isNotBlank() }?.let { name ->
                predicates.add(criteriaBuilder.like(root.get("name"), "%$name%"))
            }
            
            // 按域名查询
            request.domain?.takeIf { it.isNotBlank() }?.let { domain ->
                predicates.add(criteriaBuilder.like(root.get("domain"), "%$domain%"))
            }
            
            // 按状态查询
            request.status?.let { status ->
                predicates.add(criteriaBuilder.equal(root.get<CommonKeyStatus>("status"), status))
            }
            
            // 按类型查询
            request.type?.let { type ->
                predicates.add(criteriaBuilder.equal(root.get<KeyChannel>("type"), type))
            }
            
            criteriaBuilder.and(*predicates.toTypedArray())
        }
        
        val pageable = PageRequest.of(
            request.pageNumber,
            request.pageSize,
            Sort.by(Sort.Direction.DESC, "createdAt")
        )
        
        val page = commonTokenKeyRepository.findAll(spec, pageable)
        
        return PageResponse(
            content = page.content.map { key ->
                val response = key.toCommonResponse()
                // 如果是 Claude Code 类型，添加刷新信息
                if (key.type == KeyChannel.ClaudeCode) {
                    key.id?.let { keyId ->
                        val (_, refreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(keyId)
                        response.claudeCodeRefreshInfo = refreshInfo?.toResponse()
                    }
                }
                // 如果是 Google Vertex AI 类型，添加刷新信息
                if (key.type == KeyChannel.GoogleVertexAI) {
                    key.id?.let { keyId ->
                        val (_, refreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(keyId)
                        response.googleVertexAIRefreshInfo = refreshInfo?.toResponse()
                    }
                }
                response
            },
            total = page.totalElements,
            totalPages = page.totalPages
        )
    }

    /**
     * 获取所有活跃的密钥
     */
    fun getAllActiveKeys(): List<CommonTokenKeyResponse> {
        val keys = commonTokenKeyRepository.findByStatus(CommonKeyStatus.ACTIVE)
        return keys.map { key ->
            val response = key.toCommonResponse()
            // 如果是 Claude Code 类型，添加刷新信息
            if (key.type == KeyChannel.ClaudeCode) {
                val keyId = key.id
                if (keyId != null) {
                    val (_, refreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(keyId)
                    response.claudeCodeRefreshInfo = refreshInfo?.toResponse()
                }
            }
            response
        }
    }

    /**
     * 获取指定类型的所有活跃密钥
     */
    fun getActiveKeysByType(type: KeyChannel): List<CommonTokenKeyResponse> {
        val keys = commonTokenKeyRepository.findByStatusAndType(CommonKeyStatus.ACTIVE, type)
        return keys.map { key ->
            val response = key.toCommonResponse()
            // 如果是 Claude Code 类型，添加刷新信息
            if (key.type == KeyChannel.ClaudeCode) {
                val keyId = key.id
                if (keyId != null) {
                    val (_, refreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(keyId)
                    response.claudeCodeRefreshInfo = refreshInfo?.toResponse()
                }
            }
            response
        }
    }

    /**
     * 手动刷新 Google Vertex AI 密钥的 token
     */
    fun flushGoogleVertexAIToken(id: Long): CommonTokenKeyResponse {
        log.info { "Flushing token for Google Vertex AI key with ID: $id" }
        
        val (key, refreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(id)
        
        if (key.type != KeyChannel.GoogleVertexAI) {
            throw InvalidParameterException("该密钥不是 Google Vertex AI 类型")
        }
        
        if (refreshInfo == null) {
            throw ResourceNotFoundException("找不到刷新信息")
        }
        
        // 使用runBlocking调用挂起函数
        val refreshResult = try {
            runBlocking {
                googleVertexAIFlushService.refreshGoogleVertexAIToken(key, refreshInfo)
            }
        } catch (e: Exception) {
            log.error { "Failed to flush token for Google Vertex AI key with ID: $id, error: ${e.message}" }
            
            // 如果是TokenRefreshException且包含"服务账户无效或已禁用"错误，更新密钥状态为AUTH_FAILED
            if (e is TokenRefreshException && e.message.contains("服务账户无效或已禁用")) {
                log.warn { "Marking Google Vertex AI key with ID: $id as AUTH_FAILED due to invalid service account" }
                tokenManagementService.updateKeyStatus(id, CommonKeyStatus.AUTH_FAILED)
            }
            
            // 如果已经是我们的业务异常，直接抛出
            if (e is InvalidParameterException || e is ResourceNotFoundException || e is KeyManagementException || e is TokenRefreshException) {
                throw e
            }
            // 否则包装成 KeyManagementException
            throw KeyManagementException(e.message ?: "刷新token失败，请检查服务账户凭据是否有效", e)
        }
        
        if (refreshResult != null) {
            // 更新访问令牌
            val savedKey = tokenManagementService.refreshGoogleVertexAIAccessToken(
                refreshResult.keyId,
                refreshResult.accessToken,
                refreshResult.expiresAt
            )
            
            // 更新内存中的密钥
            keyManageService.updateKey(savedKey)
            
            log.info { "Successfully flushed token for Google Vertex AI key with ID: $id" }
            
            val response = savedKey.toCommonResponse()
            val (_, updatedRefreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(id)
            response.googleVertexAIRefreshInfo = updatedRefreshInfo?.toResponse()
            return response
        } else {
            log.error { "Failed to flush token for Google Vertex AI key with ID: $id" }
            throw KeyManagementException("刷新token失败，请检查服务账户凭据是否有效")
        }
    }

    /**
     * 远程刷新结果同步
     * 成功的ID需要根据数据库的最新数据刷新到内存里，失败的ID需要从内存里移除掉
     * @param successIds 刷新成功的密钥ID列表
     * @param failedIds 刷新失败的密钥ID列表
     */
    fun remoteFlush(successIds: List<Long>, failedIds: List<Long>) {
        val startTime = System.currentTimeMillis()
        log.info { "Remote Flush | Processing remote flush results | Success IDs: $successIds, Failed IDs: $failedIds" }

        try {
            // 处理成功的ID：从数据库重新加载最新数据并更新到内存
            if (successIds.isNotEmpty()) {
                log.info { "Remote Flush | Processing ${successIds.size} successful keys" }

                successIds.forEach { keyId ->
                    try {
                        // 从数据库获取最新的密钥数据
                        val latestKey = commonTokenKeyRepository.findById(keyId).orElse(null)
                        if (latestKey != null) {
                            // 更新内存中的密钥数据
                            keyManageService.updateKey(latestKey)
                            log.debug { "Remote Flush | Successfully updated key $keyId in memory from database" }
                        } else {
                            log.warn { "Remote Flush | Key $keyId not found in database, skipping memory update" }
                        }
                    } catch (e: Exception) {
                        log.error(e) { "Remote Flush | Failed to update key $keyId in memory: ${e.message}" }
                    }
                }

                log.info { "Remote Flush | Successfully processed ${successIds.size} successful keys" }
            }

            // 处理失败的ID：从内存中移除
            if (failedIds.isNotEmpty()) {
                log.info { "Remote Flush | Processing ${failedIds.size} failed keys for removal" }

                failedIds.forEach { keyId ->
                    try {
                        // 从数据库获取密钥信息以便从内存中移除
                        val keyToRemove = commonTokenKeyRepository.findById(keyId).orElse(null)
                        if (keyToRemove != null) {
                            // 从内存中移除密钥
                            keyManageService.removeKey(keyToRemove)
                            log.debug { "Remote Flush | Successfully removed key $keyId from memory" }
                        } else {
                            log.warn { "Remote Flush | Key $keyId not found in database, cannot remove from memory" }
                        }
                    } catch (e: Exception) {
                        log.error(e) { "Remote Flush | Failed to remove key $keyId from memory: ${e.message}" }
                    }
                }

                log.info { "Remote Flush | Successfully processed ${failedIds.size} failed keys for removal" }
            }

            val duration = System.currentTimeMillis() - startTime
            log.info { "Remote Flush | Remote flush completed | Success: ${successIds.size} updated, ${failedIds.size} removed | Duration: ${duration}ms" }

        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log.error(e) { "Remote Flush | Remote flush failed | Error: ${e.message} | Duration: ${duration}ms" }
            throw KeyManagementException("远程刷新同步失败: ${e.message}")
        }
    }

    /**
     * 手动刷新 Claude Code 密钥的 token
     */
    fun flushClaudeCodeToken(id: Long): CommonTokenKeyResponse {
        log.info { "Flushing token for Claude Code key with ID: $id" }
        
        val (key, refreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(id)
        
        if (key.type != KeyChannel.ClaudeCode) {
            throw InvalidParameterException("该密钥不是 Claude Code 类型")
        }
        
        if (refreshInfo == null) {
            throw ResourceNotFoundException("找不到刷新信息")
        }
        
        // 使用runBlocking调用挂起函数
        val refreshResult = runBlocking {
            claudeCodeFlushService.refreshClaudeCodeToken(key, refreshInfo)
        }
        
        if (refreshResult != null) {
            // 更新访问令牌和刷新令牌
            val savedKey = tokenManagementService.refreshClaudeCodeAccessToken(
                refreshResult.keyId,
                refreshResult.accessToken,
                refreshResult.expiresAt,
                refreshResult.refreshToken
            )
            
            // 更新内存中的密钥
            keyManageService.updateKey(savedKey)
            
            log.info { "Successfully flushed token for Claude Code key with ID: $id" }
            
            val response = savedKey.toCommonResponse()
            val (_, updatedRefreshInfo) = tokenManagementService.getClaudeCodeKeyWithRefreshInfo(id)
            response.claudeCodeRefreshInfo = updatedRefreshInfo?.toResponse()
            return response
        } else {
            log.error { "Failed to flush token for Claude Code key with ID: $id" }
            throw KeyManagementException("刷新token失败，请检查refresh token是否有效")
        }
    }

    /**
     * 批量创建通用密钥
     */
    fun createKeysBatch(request: BatchCreateCommonTokenKeyRequest): BatchCreateResponse {
        log.info { "Creating batch of ${request.keys.size} keys" }
        
        val successful = mutableListOf<CommonTokenKeyResponse>()
        val failed = mutableListOf<BatchCreateFailure>()
        
        request.keys.forEachIndexed { index, keyRequest ->
            try {
                val response = createKey(keyRequest)
                successful.add(response)
                log.debug { "Successfully created key at index $index with ID: ${response.id}" }
            } catch (e: Exception) {
                log.error { "Failed to create key at index $index: ${e.message}" }
                failed.add(BatchCreateFailure(
                    index = index,
                    data = keyRequest,
                    error = e.message ?: "Unknown error"
                ))
            }
        }
        
        log.info { "Batch creation completed: ${successful.size} successful, ${failed.size} failed" }
        return BatchCreateResponse(successful = successful, failed = failed)
    }

    /**
     * 批量创建Claude Code密钥
     */
    fun createClaudeCodeKeysBatch(request: BatchCreateClaudeCodeKeyRequest): BatchCreateResponse {
        log.info { "Creating batch of ${request.keys.size} Claude Code keys" }
        
        val successful = mutableListOf<CommonTokenKeyResponse>()
        val failed = mutableListOf<BatchCreateFailure>()
        
        request.keys.forEachIndexed { index, keyRequest ->
            try {
                val response = createKey(keyRequest)
                successful.add(response)
                log.debug { "Successfully created Claude Code key at index $index with ID: ${response.id}" }
            } catch (e: Exception) {
                log.error { "Failed to create Claude Code key at index $index: ${e.message}" }
                failed.add(BatchCreateFailure(
                    index = index,
                    data = keyRequest,
                    error = e.message ?: "Unknown error"
                ))
            }
        }
        
        log.info { "Claude Code batch creation completed: ${successful.size} successful, ${failed.size} failed" }
        return BatchCreateResponse(successful = successful, failed = failed)
    }

    /**
     * 导入 Google Vertex AI 服务账户 JSON（简单版本）
     */
    fun importGoogleVertexAIKey(serviceAccountJson: String): CommonTokenKeyResponse {
        log.info { "Importing Google Vertex AI key from service account JSON (simple)" }

        // 创建默认的导入请求
        val request = ImportGoogleVertexAIKeyRequest(
            serviceAccountJson = serviceAccountJson,
            name = null,
            supportModels = emptyList(),
            autoDisable = false,
            modelMapping = null,
            quota = null,
            weight = 1.0,
            windowSize = 100,
            epsilon = 0.05,
            errThreshold = 0.5
        )

        return importGoogleVertexAIKey(request)
    }

    /**
     * 导入 Google Vertex AI 服务账户 JSON（带完整参数）
     */
    fun importGoogleVertexAIKey(request: ImportGoogleVertexAIKeyRequest): CommonTokenKeyResponse {
        log.info { "Importing Google Vertex AI key from service account JSON with parameters" }

        // 解析 JSON
        val serviceAccount = try {
            objectMapper.readValue<GoogleServiceAccountJson>(request.serviceAccountJson)
        } catch (e: Exception) {
            log.error { "Failed to parse service account JSON: ${e.message}" }
            throw IllegalArgumentException("无效的服务账户 JSON 格式: ${e.message}")
        }

        // 验证必要字段
        if (serviceAccount.type != "service_account") {
            throw IllegalArgumentException("JSON 文件不是有效的服务账户格式")
        }

        // 创建密钥实体，使用传入的参数
        // 为 Google Vertex AI 密钥自动设置 projectId 到 metadata 中
        val metadataMap = mutableMapOf<String, String>()
        metadataMap["projectId"] = serviceAccount.project_id

        // 如果请求中有额外的 metadata，合并进去
        request.metadata?.let { additionalMetadata ->
            try {
                metadataMap.putAll(additionalMetadata)
            } catch (e: Exception) {
                log.warn { "Failed to parse additional metadata JSON: ${e.message}" }
            }
        }

        val metadataJson = objectMapper.writeValueAsString(metadataMap)

        val key = CommonTokenKey(
            accessToken = null, // Google Vertex AI 初始没有 access token
            domain = null,
            type = KeyChannel.GoogleVertexAI,
            status = CommonKeyStatus.ACTIVE,
            name = request.name ?: "${serviceAccount.project_id} - ${serviceAccount.client_email}",
            supportModels = request.supportModels,
            autoDisable = request.autoDisable,
            modelMapping = request.modelMapping,
            metadata = metadataJson,
            quota = request.quota,
            weight = request.weight.toBigDecimal(),
            windowSize = request.windowSize,
            epsilon = request.epsilon.toBigDecimal(),
            errThreshold = request.errThreshold.toBigDecimal(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        // 创建 Google Vertex AI 刷新信息
        val refreshInfo = GoogleVertexAITokenRefreshInfo(
            commonKeyId = 0, // 将在 tokenManagementService 中设置
            projectId = serviceAccount.project_id,
            clientEmail = serviceAccount.client_email,
            privateKeyId = serviceAccount.private_key_id,
            privateKey = serviceAccount.private_key,
            tokenUri = serviceAccount.token_uri,
            scope = "https://www.googleapis.com/auth/cloud-platform",
            expiresAt = null, // 初始没有过期时间
            lastRefreshTime = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        val savedKey = tokenManagementService.createTokenKey(key, googleRefreshInfo = refreshInfo)

        // 立即触发第一次刷新以获取 access token
        val refreshedResponse = runBlocking {
            try {
                log.info { "Triggering initial token refresh for Google Vertex AI key" }
                val keyId = savedKey.id
                if (keyId != null) {
                    // 获取刷新信息
                    val (tokenKey, googleRefreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(keyId)
                    if (googleRefreshInfo != null) {
                        // 使用 Google Vertex AI flush service 刷新 token
                        val refreshResult = googleVertexAIFlushService.refreshGoogleVertexAIToken(tokenKey, googleRefreshInfo)

                        if (refreshResult != null) {
                            // 更新 access token
                            val refreshedKey = tokenManagementService.refreshGoogleVertexAIAccessToken(
                                refreshResult.keyId,
                                refreshResult.accessToken,
                                refreshResult.expiresAt
                            )

                            // 如果刷新成功且密钥活跃，更新内存中的密钥
                            if (refreshedKey.status == CommonKeyStatus.ACTIVE && !refreshedKey.accessToken.isNullOrBlank()) {
                                keyManageService.updateKey(refreshedKey)
                            }

                            log.info { "Successfully imported and refreshed Google Vertex AI key with ID: ${refreshedKey.id}" }
                            return@runBlocking refreshedKey.toCommonResponse()
                        }
                    }
                }
                null
            } catch (e: Exception) {
                log.error { "Failed to refresh initial token: ${e.message}" }
                // 即使刷新失败，仍然返回创建的密钥，让用户知道密钥已创建
                null
            }
        }

        // 返回结果
        val response = refreshedResponse ?: savedKey.toCommonResponse()
        val (_, updatedRefreshInfo) = tokenManagementService.getGoogleVertexAIKeyWithRefreshInfo(savedKey.id!!)
        response.googleVertexAIRefreshInfo = updatedRefreshInfo?.toResponse()

        log.info { "Successfully imported Google Vertex AI key with ID: ${savedKey.id}" }
        return response
    }
}

// 扩展函数
fun CommonTokenKey.toCommonResponse(): CommonTokenKeyResponse {
    return CommonTokenKeyResponse(
        id = this.id!!,
        accessToken = this.accessToken,
        domain = this.domain,
        type = this.type,
        status = this.status,
        name = this.name,
        supportModels = this.supportModels,
        autoDisable = this.autoDisable,
        modelMapping = this.modelMapping,
        metadata = this.metadata,
        quota = this.quota,
        weight = this.weight.toDouble(),
        windowSize = this.windowSize,
        epsilon = this.epsilon.toDouble(),
        errThreshold = this.errThreshold.toDouble(),
        createdAt = this.createdAt.toString(),
        updatedAt = this.updatedAt.toString()
    )
}

fun ClaudeCodeTokenRefreshInfo.toResponse(): ClaudeCodeRefreshInfoResponse {
    return ClaudeCodeRefreshInfoResponse(
        refreshToken = this.refreshToken,
        expiresAt = this.expiresAt,
        clientId = this.clientId,
        email = this.email,
        accountType = this.accountType,
        lastRefreshTime = this.lastRefreshTime?.toString()
    )
}

fun GoogleVertexAITokenRefreshInfo.toResponse(): GoogleVertexAIRefreshInfoResponse {
    return GoogleVertexAIRefreshInfoResponse(
        projectId = this.projectId,
        clientEmail = this.clientEmail,
        privateKeyId = this.privateKeyId,
        expiresAt = this.expiresAt,
        lastRefreshTime = this.lastRefreshTime?.toString()
    )
}