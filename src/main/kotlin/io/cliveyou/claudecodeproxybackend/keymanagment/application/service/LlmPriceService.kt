package io.cliveyou.claudecodeproxybackend.keymanagment.application.service

import io.cliveyou.claudecodeproxybackend.common.exception.BusinessException
import io.cliveyou.claudecodeproxybackend.common.response.PageResponse
import io.cliveyou.claudecodeproxybackend.keymanagment.facade.dto.*
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.SpConsoleSystemLlmPrice
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.SpConsoleSystemLlmPriceRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.SpConsoleSystemModelRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * LLM价格服务实现类
 */
@Service
class LlmPriceService(
    private val llmPriceRepository: SpConsoleSystemLlmPriceRepository,
    private val systemModelRepository: SpConsoleSystemModelRepository
) {

    /**
     * 分页查询LLM价格列表
     */
    suspend fun queryPage(request: LlmPriceQueryRequest): PageResponse<LlmPriceResponse> = withContext(Dispatchers.IO) {
        val pageable = PageRequest.of(
            request.page - 1, 
            request.size, 
            Sort.by(Sort.Direction.DESC, "createTime")
        )
        
        val page = llmPriceRepository.findByConditions(
            request.systemModelId,
            request.type,
            request.statuses,
            pageable
        )

        val responses = page.content.map { price ->
            convertToResponse(price)
        }

        PageResponse(
            content = responses,
            total = page.totalElements,
            totalPages = page.totalPages
        )
    }

    /**
     * 创建LLM价格
     */
    @Transactional
    suspend fun create(request: LlmPriceCreateRequest): LlmPriceResponse = withContext(Dispatchers.IO) {
        // 验证系统模型是否存在
        systemModelRepository.findById(request.systemModelId).orElseThrow {
            BusinessException(code = "MODEL_NOT_FOUND", message = "系统模型不存在: ${request.systemModelId}")
        }

        // 检查是否已存在相同模型和类型的价格配置
        if (llmPriceRepository.existsBySystemModelIdAndType(request.systemModelId, request.type)) {
            throw BusinessException(code = "PRICE_ALREADY_EXISTS", message = "该模型和类型的价格配置已存在")
        }

        val price = SpConsoleSystemLlmPrice(
            systemModelId = request.systemModelId,
            type = request.type,
            statuses = request.statuses,
            createTime = System.currentTimeMillis(),
            audioCachePrompt = request.audioCachePrompt,
            audioCompletion = request.audioCompletion,
            audioPrompt = request.audioPrompt,
            imageCachePrompt = request.imageCachePrompt,
            imageCompletion = request.imageCompletion,
            imagePrompt = request.imagePrompt,
            reasoningCompletion = request.reasoningCompletion,
            textCachePrompt = request.textCachePrompt,
            textCachePromptWrite1h = request.textCachePromptWrite1h,
            textCachePromptWrite5m = request.textCachePromptWrite5m,
            textCompletion = request.textCompletion,
            textPrompt = request.textPrompt
        )

        val savedPrice = llmPriceRepository.save(price)
        convertToResponse(savedPrice)
    }

    /**
     * 根据ID获取LLM价格详情
     */
    suspend fun getById(id: Long): LlmPriceResponse = withContext(Dispatchers.IO) {
        val price = llmPriceRepository.findById(id).orElseThrow {
            BusinessException(code = "PRICE_NOT_FOUND", message = "价格信息不存在: $id")
        }
        convertToResponse(price)
    }

    /**
     * 更新LLM价格
     */
    @Transactional
    suspend fun update(id: Long, request: LlmPriceUpdateRequest): LlmPriceResponse = withContext(Dispatchers.IO) {
        val existingPrice = llmPriceRepository.findById(id).orElseThrow {
            BusinessException(code = "PRICE_NOT_FOUND", message = "价格信息不存在: $id")
        }

        val updatedPrice = existingPrice.copy(
            type = request.type ?: existingPrice.type,
            statuses = request.statuses ?: existingPrice.statuses,
            audioCachePrompt = request.audioCachePrompt ?: existingPrice.audioCachePrompt,
            audioCompletion = request.audioCompletion ?: existingPrice.audioCompletion,
            audioPrompt = request.audioPrompt ?: existingPrice.audioPrompt,
            imageCachePrompt = request.imageCachePrompt ?: existingPrice.imageCachePrompt,
            imageCompletion = request.imageCompletion ?: existingPrice.imageCompletion,
            imagePrompt = request.imagePrompt ?: existingPrice.imagePrompt,
            reasoningCompletion = request.reasoningCompletion ?: existingPrice.reasoningCompletion,
            textCachePrompt = request.textCachePrompt ?: existingPrice.textCachePrompt,
            textCachePromptWrite1h = request.textCachePromptWrite1h ?: existingPrice.textCachePromptWrite1h,
            textCachePromptWrite5m = request.textCachePromptWrite5m ?: existingPrice.textCachePromptWrite5m,
            textCompletion = request.textCompletion ?: existingPrice.textCompletion,
            textPrompt = request.textPrompt ?: existingPrice.textPrompt
        )

        val savedPrice = llmPriceRepository.save(updatedPrice)
        convertToResponse(savedPrice)
    }

    /**
     * 删除LLM价格
     */
    @Transactional
    suspend fun delete(id: Long) = withContext(Dispatchers.IO) {
        if (!llmPriceRepository.existsById(id)) {
            throw BusinessException(code = "PRICE_NOT_FOUND", message = "价格信息不存在: $id")
        }
        llmPriceRepository.deleteById(id)
    }

    /**
     * 根据系统模型ID查询价格列表
     */
    suspend fun getBySystemModelId(systemModelId: Long): List<LlmPriceResponse> = withContext(Dispatchers.IO) {
        val prices = llmPriceRepository.findBySystemModelId(systemModelId)
        prices.map { convertToResponse(it) }
    }

    /**
     * 转换实体到响应DTO
     */
    private suspend fun convertToResponse(price: SpConsoleSystemLlmPrice): LlmPriceResponse {
        val systemModel = price.systemModelId?.let {
            systemModelRepository.findById(it).orElse(null)
        }

        return LlmPriceResponse(
            id = price.id!!,
            systemModelId = price.systemModelId!!,
            systemModelName = systemModel?.modelName,
            type = price.type!!,
            statuses = price.statuses!!,
            createTime = price.createTime,
            audioCachePrompt = price.audioCachePrompt,
            audioCompletion = price.audioCompletion,
            audioPrompt = price.audioPrompt,
            imageCachePrompt = price.imageCachePrompt,
            imageCompletion = price.imageCompletion,
            imagePrompt = price.imagePrompt,
            reasoningCompletion = price.reasoningCompletion,
            textCachePrompt = price.textCachePrompt,
            textCachePromptWrite1h = price.textCachePromptWrite1h,
            textCachePromptWrite5m = price.textCachePromptWrite5m,
            textCompletion = price.textCompletion,
            textPrompt = price.textPrompt
        )
    }
}