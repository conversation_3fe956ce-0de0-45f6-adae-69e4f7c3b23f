package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository

import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ClaudeCodeTokenRefreshInfo
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ClaudeType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface ClaudeCodeTokenRefreshInfoRepository : JpaRepository<ClaudeCodeTokenRefreshInfo, Long> {

    /**
     * 根据 CommonTokenKey ID 查找刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     * @return 刷新信息
     */
    fun findByCommonKeyId(commonKeyId: Long): Optional<ClaudeCodeTokenRefreshInfo>

    /**
     * 查找即将在指定时间内过期的刷新信息
     * @param expirationThreshold 过期时间阈值（时间戳秒）
     * @return 即将过期的刷新信息列表
     */
    @Query("""
        SELECT r FROM ClaudeCodeTokenRefreshInfo r 
        WHERE r.expiresAt IS NOT NULL 
        AND r.expiresAt <= :expirationThreshold
    """)
    fun findRefreshInfoExpiringBefore(
        @Param("expirationThreshold") expirationThreshold: Long
    ): List<ClaudeCodeTokenRefreshInfo>

    /**
     * 根据客户端ID查找刷新信息
     * @param clientId 客户端ID
     * @return 刷新信息列表
     */
    fun findByClientId(clientId: String): List<ClaudeCodeTokenRefreshInfo>

    /**
     * 根据邮箱查找刷新信息
     * @param email 邮箱地址
     * @return 刷新信息列表
     */
    fun findByEmail(email: String): List<ClaudeCodeTokenRefreshInfo>

    /**
     * 根据账户类型查找刷新信息
     * @param accountType Claude账户类型
     * @return 刷新信息列表
     */
    fun findByAccountType(accountType: ClaudeType): List<ClaudeCodeTokenRefreshInfo>

    /**
     * 删除指定 CommonTokenKey ID 的刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     */
    fun deleteByCommonKeyId(commonKeyId: Long)

    /**
     * 检查指定 CommonTokenKey ID 是否存在刷新信息
     * @param commonKeyId CommonTokenKey 的 ID
     * @return 是否存在
     */
    fun existsByCommonKeyId(commonKeyId: Long): Boolean
}