package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.CommonAuthKey
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnDefault
import org.hibernate.annotations.Type
import java.math.BigDecimal
import java.time.LocalDateTime

@Entity
@Table(name = " sp_platform_common_code_key")
data class CommonTokenKey(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 访问令牌
     */
    @Column(name = "access_token", columnDefinition = "TEXT")
    val accessToken: String? = null,


    @Column(name = "domain", nullable = true)
    val domain: String? = null,

    /**
     * 密钥渠道类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    val type: KeyChannel = KeyChannel.Openai,

    /**
     * 密钥状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: CommonKeyStatus = CommonKeyStatus.ACTIVE,


    /**
     * 密钥名称/描述
     */
    @Column(name = "name")
    val name: String? = null,


    @ColumnDefault("'{}'")
    @Type(ListArrayType::class)
    @Column(nullable = false, columnDefinition = "text[]")
    val supportModels: List<String> = emptyList(),

    /**
     * 是否自动禁用
     */
    @ColumnDefault("false")
    @Column(name = "auto_disable", nullable = false)
    val autoDisable: Boolean = false,

    /**
     * 模型映射配置 JSON
     * key: 原始模型名, value: 映射模型名
     */
    @Column(name = "model_mapping", columnDefinition = "TEXT")
    val modelMapping: String? = null,

    /**
     * 元数据配置 JSON
     * key: 元数据键, value: 元数据值
     * 对于 Google Vertex AI，存储 projectId 等信息
     */
    @Column(name = "metadata", columnDefinition = "TEXT")
    val metadata: String? = null,

    /**
     * 配额金额
     */
    @Column(name = "quota", precision = 19, scale = 2)
    val quota: BigDecimal? = null,

    /**
     * 密钥权重，用于调度器选择密钥时的权重计算
     */
    @ColumnDefault("1.0")
    @Column(name = "weight", precision = 5, scale = 2, nullable = false)
    val weight: BigDecimal = 1.0.toBigDecimal(),

    /**
     * 滑动窗口大小，用于统计错误率
     */
    @ColumnDefault("100")
    @Column(name = "window_size", nullable = false)
    val windowSize: Int = 100,

    /**
     * 探索率 ε，控制随机选择的概率
     */
    @ColumnDefault("0.05")
    @Column(name = "epsilon", precision = 5, scale = 3, nullable = false)
    val epsilon: BigDecimal = 0.05.toBigDecimal(),

    /**
     * 错误阈值，超过此值的密钥将被过滤
     */
    @ColumnDefault("0.5")
    @Column(name = "err_threshold", precision = 5, scale = 3, nullable = false)
    val errThreshold: BigDecimal = 0.5.toBigDecimal(),

    /**
     * 最小样本数阈值，样本数低于此值时不应用错误率过滤
     */
    @ColumnDefault("5")
    @Column(name = "min_sample_size", nullable = false)
    val minSampleSize: Int = 5,

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)

fun CommonTokenKey.toAuthKey(): CommonAuthKey? {
    val keyId = this.id
    val token = this.accessToken
    return if (keyId != null && !token.isNullOrBlank()) {
        val modelMappingMap = modelMapping?.let {
            try {
                ObjectMapper().readValue<Map<String, String>>(it)
            } catch (e: Exception) {
                null
            }
        }
        val metadataMap = metadata?.let {
            try {
                ObjectMapper().readValue<Map<String, String>>(it)
            } catch (e: Exception) {
                null
            }
        }
        CommonAuthKey(
            id = keyId,
            token = token,
            channel = type,
            modelMapping = modelMappingMap,
            metadata = metadataMap
        )
    } else {
        null
    }
}

enum class CommonKeyStatus {
    ACTIVE,
    DISABLED,
    AUTH_FAILED
}