package io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.remote.flush

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ClaudeCodeTokenRefreshInfo
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.stereotype.Service
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration

@Service
class ClaudeCodeFlushService(
    private val objectMapper: ObjectMapper
) {

    private val logger = KotlinLogging.logger {}

    private val httpClient = HttpClient.newBuilder()
        .connectTimeout(Duration.ofSeconds(30))
        .build()


    suspend fun refreshAccessToken(refreshToken: String, clientId: String): ClaudeTokenResponse? {
        return withContext(Dispatchers.IO) {
            try {
                val request = ClaudeTokenRefreshRequest(
                    refreshToken = refreshToken,
                    clientId = clientId
                )

                val requestBody = objectMapper.writeValueAsString(request)

                val httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(CLAUDE_TOKEN_URL))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build()

                logger.debug { "发送Claude token刷新请求: clientId=$clientId" }

                val response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString())

                val content = response.body()
                if (response.statusCode() == 200) {
                    val tokenResponse = objectMapper.readValue<ClaudeTokenResponse>(content)
                    logger.info { "Claude token刷新成功: $content" }
                    tokenResponse
                } else {
                    logger.error { "Claude token刷新失败: clientId=$clientId, status=${response.statusCode()}, body=$content" }
                    null
                }
            } catch (e: Exception) {
                logger.error(e) { "Claude token刷新异常: clientId=$clientId" }
                null
            }
        }
    }

    /**
     * 刷新Claude Code的token
     * @param tokenKey 通用密钥
     * @param refreshInfo 刷新信息
     * @return 刷新结果，包含新的accessToken和expiresAt
     */
    suspend fun refreshClaudeCodeToken(tokenKey: CommonTokenKey, refreshInfo: ClaudeCodeTokenRefreshInfo): ClaudeRefreshResult? {
        return try {
            log.info { "Refresh Token | Start refresh for key ${tokenKey.id} | Info: Attempting token refresh" }

            val tokenResponse = refreshAccessToken(refreshInfo.refreshToken, refreshInfo.clientId)

            if (tokenResponse != null) {
                val result = ClaudeRefreshResult(
                    keyId = tokenKey.id!!,
                    accessToken = tokenResponse.accessToken,
                    refreshToken = tokenResponse.refreshToken,
                    expiresAt = calculateExpirationTimestamp(tokenResponse.expiresIn)
                )

                log.info { "Refresh Token | Token refresh for key ${tokenKey.id} | Success: New expiration at ${result.expiresAt}" }
                result
            } else {
                log.error { "Refresh Token | Token refresh for key ${tokenKey.id} | Failed: Token response is null | Suggestion: Check refresh token validity" }
                null
            }
        } catch (e: Exception) {
            log.error(e) { "Refresh Token | Token refresh for key ${tokenKey.id} | Failed: ${e.message} | Suggestion: Check network connectivity and refresh token validity" }
            null
        }
    }

    /**
     * 批量刷新Claude Code token
     * @param pairs 密钥和刷新信息的配对列表
     * @return 刷新结果
     */
    suspend fun refreshClaudeCodeTokens(pairs: List<Pair<CommonTokenKey, ClaudeCodeTokenRefreshInfo>>): TokenRefreshBatchResult {
        val successfulResults = mutableListOf<ClaudeRefreshResult>()
        val failedPairs = mutableListOf<Pair<CommonTokenKey, ClaudeCodeTokenRefreshInfo>>()

        log.info { "Refresh Token | Batch refresh started | Info: Processing ${pairs.size} keys" }

        for ((tokenKey, refreshInfo) in pairs) {
            val result = refreshClaudeCodeToken(tokenKey, refreshInfo)
            if (result != null) {
                successfulResults.add(result)
            } else {
                failedPairs.add(tokenKey to refreshInfo)
            }
        }

        val result = TokenRefreshBatchResult(
            successfulResults = successfulResults,
            failedPairs = failedPairs,
            totalProcessed = pairs.size
        )

        log.info { "Refresh Token | Batch refresh completed | Success: ${successfulResults.size}/${pairs.size} keys refreshed successfully" }

        return result
    }

    /**
     * 计算token过期时间戳
     */
    fun calculateExpirationTimestamp(expiresIn: Int): Long {
        return System.currentTimeMillis() / 1000 + expiresIn
    }


    companion object {
        private val log = KotlinLogging.logger {}
        private const val CLAUDE_TOKEN_URL = "https://console.anthropic.com/v1/oauth/token"
    }
}

/**
 * Claude Token刷新请求DTO
 */
data class ClaudeTokenRefreshRequest(
    @get:JsonProperty("grant_type")
    val grantType: String = "refresh_token",
    @get:JsonProperty("refresh_token")
    val refreshToken: String,
    @get:JsonProperty("client_id")
    val clientId: String
)

data class ClaudeTokenResponse(
    @JsonProperty("token_type")
    val tokenType: String,
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("expires_in")
    val expiresIn: Int,
    @JsonProperty("refresh_token")
    val refreshToken: String,
    val scope: String,
)

/**
 * Claude刷新结果
 */
data class ClaudeRefreshResult(
    val keyId: Long,
    val accessToken: String,
    val refreshToken: String,
    val expiresAt: Long
)

/**
 * 批量刷新结果
 */
data class TokenRefreshBatchResult(
    val successfulResults: List<ClaudeRefreshResult>,
    val failedPairs: List<Pair<CommonTokenKey, ClaudeCodeTokenRefreshInfo>>,
    val totalProcessed: Int
)