import { apiClient } from "@/lib/apiClient";
import type { OrganizationModelResponse, updateModelRequest } from "./model-model";
import type { FetchParams, FetchResponse } from "@/components/table/types";



export const modelApi = {
    getModelList: async (organizationId: string) => {
        const response = await apiClient.get<OrganizationModelResponse[]>({
            url: "/api/private/organizationModel/selectOrganizationModelByOrganizationId",
            params: {
                organizationId
            }
        });
        return response;
    },

    pageModel: async (organizationId: string | undefined, params: FetchParams) => {
        if (!organizationId) {
            return {
                content: [],
                total: 0
            }
        }
        const response = await apiClient.get<FetchResponse<OrganizationModelResponse>>({
            url: "/api/private/organizationModel/pageOrganizationModelByOrganizationId",
            params: {
                organizationId,
                ...params.searchParams,
                pageSize: params.pagination.pageSize,
                pageNumber: params.pagination.pageIndex,
            }
        });
        return response;
    },


    updateModel: async (requests: updateModelRequest[]) => {
        const response = await apiClient.post<OrganizationModelResponse[]>({
            url: "/api/private/organizationModel/batch/update",
            data: requests
        });
        return response;
    }
}