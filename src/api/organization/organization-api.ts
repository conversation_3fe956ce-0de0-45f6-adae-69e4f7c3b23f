import { apiClient } from "@/lib/apiClient";
import type { OrganizationModal } from "./organization-modal";
import type { FetchParams, FetchResponse } from "@/components/table/types";

export const organizationApi = {
    /**
     * 获取当前组织信息
     * @returns 当前组织信息
     */
    getCurrentOrganization: async (): Promise<OrganizationModal> => {
        return await apiClient.get({
            url: "/api/private/organizationInfo/current/organization",
        });
    },

    /**
     * 切换当前组织
     * @param organizationId 组织ID
     * @returns 切换后的组织信息
     */
    changeCurrentOrganization: async (organizationId: string): Promise<OrganizationModal> => {
        return await apiClient.post({
            url: "/api/private/organizationInfo/current/organization/change",
            params: {
                organizationId
            }
        });
    },

    updateOrganizationInfo: async (organizationId: string, organizationName: string): Promise<OrganizationModal> => {
        return await apiClient.put({
            url: "/api/private/organizationInfo/updateOrganizationInfo",
            params: {
                organizationId, organizationName
            }
        });
    },

    /**
     * 获取组织列表
     * @returns 组织列表
     */
    getOrganizationList: async (): Promise<OrganizationModal[]> => {
        return await apiClient.get({
            url: "/api/private/organizationInfo/selectOrganizationInfoAll",
        });
    },

    createOrganization: async (organizationName: string): Promise<OrganizationModal> => {
        return await apiClient.post({
            url: "/api/private/organizationInfo/persistenceOrganizationInfo",
            params: {
                organizationName
            }
        });
    },

    deleteOrganization: async (organizationIds: string[]): Promise<void> => {
        return await apiClient.delete({
            url: "/api/private/organizationInfo/deleteOrganizationInfo",
            data: organizationIds
        });
    },


    pageOrganization: async (params: FetchParams): Promise<FetchResponse<OrganizationModal>> => {
        const res = await organizationApi.getOrganizationList();
        console.log(JSON.stringify(params.sort));
        let sortedData = [...res];
        const name = params.searchParams.organizationName
        const statuses = params.searchParams.statuses

        if (name) {
            sortedData = sortedData.filter((item) => item.organizationName.toLowerCase().includes(name.toLowerCase()))
        }

        if (statuses) {
            sortedData = sortedData.filter((item) => item.statuses == statuses)
        }

        // 处理排序
        if (params.sort && params.sort.length > 0 && params.direction && params.direction.length > 0) {
            const sortField = params.sort[0];
            const sortDirection = params.direction[0];


            sortedData.sort((a, b) => {
                let aValue: any;
                let bValue: any;

                // 根据排序字段获取值
                switch (sortField) {
                    case 'balance':
                        aValue = BigInt(a.balance || 0);
                        bValue = BigInt(b.balance || 0);
                        break;
                    case 'monthUsage':
                        aValue = BigInt(a.monthUsage || 0);
                        bValue = BigInt(b.monthUsage || 0);
                        break;
                    case 'accumulatedUsage':
                        aValue = BigInt(a.accumulatedUsage || '0');
                        bValue = BigInt(b.accumulatedUsage || '0');
                        break;
                    default:
                        aValue = (a as any)[sortField];
                        bValue = (b as any)[sortField];
                }

                // BigInt 数值比较
                if (typeof aValue === 'bigint' && typeof bValue === 'bigint') {
                    if (sortDirection === 'ASC') {
                        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                    } else {
                        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                    }
                }

                // 普通数值比较
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    const result = sortDirection === 'ASC' ? aValue - bValue : bValue - aValue;
                    return result;
                }

                // 字符串比较
                const aStr = String(aValue || '').toLowerCase();
                const bStr = String(bValue || '').toLowerCase();

                if (sortDirection === 'ASC') {
                    return aStr.localeCompare(bStr);
                } else {
                    return bStr.localeCompare(aStr);
                }
            });

            console.log('排序后数据前3条:', sortedData.slice(0, 3).map(item => ({
                organizationName: item.organizationName,
                [sortField]: (item as any)[sortField]
            })));
        }

        // 分页处理
        const total = sortedData.length;
        const start = params.pagination.pageIndex * params.pagination.pageSize;
        const end = start + params.pagination.pageSize;
        const paginatedData = sortedData.slice(start, end);

        return {
            content: paginatedData,
            total: total,
        };
    },
}