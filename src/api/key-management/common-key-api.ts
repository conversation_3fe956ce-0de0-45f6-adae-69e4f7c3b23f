import type { <PERSON>tch<PERSON>ara<PERSON>, FetchResponse } from "@/components/table/types";
import type {
    CommonTokenKey,
    CreateCommonTokenKeyRequest,
    CreateClaudeCodeKeyRequest,
    UpdateCommonTokenKeyRequest,
    UpdateClaudeCodeKeyRequest,
    UpdateGoogleVertexAIKeyRequest,
    ImportGoogleVertexAIKeyRequest,
    ApiTestResult,
    KeyChannel,
    PageCommonTokenKeyRequest,
    BatchCreateCommonTokenKeyRequest,
    BatchCreateClaudeCodeKeyRequest,
    BatchCreateResponse
} from "./common-key-model";
import { apiClient } from "@/lib/apiClient";

export const commonKeyApi = {

    /**
     * 分页查询通用密钥
     */
    pageKeys: async (params: FetchParams): Promise<FetchResponse<CommonTokenKey>> => {
        const request: PageCommonTokenKeyRequest = {
            pageNumber: params.pagination.pageIndex,
            pageSize: params.pagination.pageSize,
            name: params.searchParams?.name,
            domain: params.searchParams?.domain,
            status: params.searchParams?.status,
            type: params.searchParams?.type,
        };

        const response = await apiClient.post<FetchResponse<CommonTokenKey>>({
            url: "/api/v1/common-keys/page",
            data: request
        });
        return response;
    },

    /**
     * 创建通用密钥
     */
    createKey: async (request: CreateCommonTokenKeyRequest): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: "/api/v1/common-keys",
            data: request
        });
    },

    /**
     * 创建Claude Code密钥
     */
    createClaudeCodeKey: async (request: CreateClaudeCodeKeyRequest): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: "/api/v1/common-keys/claude-code",
            data: request
        });
    },

    /**
     * 批量创建通用密钥
     */
    createKeysBatch: async (request: BatchCreateCommonTokenKeyRequest): Promise<BatchCreateResponse> => {
        return await apiClient.post<BatchCreateResponse>({
            url: "/api/v1/common-keys/batch",
            data: request
        });
    },

    /**
     * 批量创建Claude Code密钥
     */
    createClaudeCodeKeysBatch: async (request: BatchCreateClaudeCodeKeyRequest): Promise<BatchCreateResponse> => {
        return await apiClient.post<BatchCreateResponse>({
            url: "/api/v1/common-keys/claude-code/batch",
            data: request
        });
    },

    /**
     * 更新通用密钥
     */
    updateKey: async (request: UpdateCommonTokenKeyRequest): Promise<CommonTokenKey> => {
        debugger;
        return await apiClient.put<CommonTokenKey>({
            url: `/api/v1/common-keys/${request.id}`,
            data: request
        });
    },

    /**
     * 更新Claude Code密钥
     */
    updateClaudeCodeKey: async (request: UpdateClaudeCodeKeyRequest): Promise<CommonTokenKey> => {
        return await apiClient.put<CommonTokenKey>({
            url: `/api/v1/common-keys/claude-code/${request.id}`,
            data: request
        });
    },

    /**
     * 删除密钥
     */
    deleteKey: async (id: number): Promise<void> => {
        await apiClient.delete<void>({
            url: `/api/v1/common-keys/${id}`
        });
    },

    /**
     * 批量删除密钥
     */
    batchDeleteKeys: async (ids: number[]): Promise<void> => {
        await apiClient.delete<void>({
            url: "/api/v1/common-keys/batch",
            data: ids
        });
    },

    /**
     * 根据ID查询密钥
     */
    getKeyById: async (id: number): Promise<CommonTokenKey> => {
        return await apiClient.get<CommonTokenKey>({
            url: `/api/v1/common-keys/${id}`
        });
    },

    /**
     * 获取所有活跃的密钥
     */
    getAllActiveKeys: async (): Promise<CommonTokenKey[]> => {
        return await apiClient.get<CommonTokenKey[]>({
            url: "/api/v1/common-keys/active"
        });
    },

    /**
     * 获取指定类型的活跃密钥
     */
    getActiveKeysByType: async (type: KeyChannel): Promise<CommonTokenKey[]> => {
        return await apiClient.get<CommonTokenKey[]>({
            url: `/api/v1/common-keys/active/${type}`
        });
    },

    /**
     * 测试密钥
     */
    testKey: async (keyId: number, channel: string, model: string): Promise<ApiTestResult> => {
        return await apiClient.get<ApiTestResult>({
            url: `/api/key-test/${channel}/${keyId}`,
            params: {
                model
            }
        });
    },

    /**
     * 刷新Claude Code密钥token
     */
    flushClaudeCodeToken: async (id: number): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: `/api/v1/common-keys/claude-code/${id}/flush`
        });
    },

    /**
     * 导入Google Vertex AI服务账户JSON（带完整参数）
     */
    importGoogleVertexAI: async (request: ImportGoogleVertexAIKeyRequest): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: "/api/v1/common-keys/google-vertex-ai/import",
            data: request
        });
    },

    /**
     * 导入Google Vertex AI服务账户JSON（简单版本，仅JSON字符串）
     */
    importGoogleVertexAISimple: async (serviceAccountJson: string): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: "/api/v1/common-keys/google-vertex-ai/import-simple",
            data: serviceAccountJson,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    },

    /**
     * 更新Google Vertex AI密钥
     */
    updateGoogleVertexAIKey: async (request: UpdateGoogleVertexAIKeyRequest): Promise<CommonTokenKey> => {
        return await apiClient.put<CommonTokenKey>({
            url: `/api/v1/common-keys/google-vertex-ai/${request.id}`,
            data: request
        });
    },

    /**
     * 刷新Google Vertex AI密钥token
     */
    flushGoogleVertexAIToken: async (id: number): Promise<CommonTokenKey> => {
        return await apiClient.post<CommonTokenKey>({
            url: `/api/v1/common-keys/google-vertex-ai/${id}/flush`
        });
    }
}
