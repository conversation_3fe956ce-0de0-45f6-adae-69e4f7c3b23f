// 密钥渠道类型
export type KeyChannel = 
    | 'ClaudeCode' 
    | 'Openai' 
    | 'Azure' 
    | 'Agent' 
    | 'Aws' 
    | 'Anthropic' 
    | 'GoogleAiStudio'
    | 'GoogleVertexAI'
    | 'BingSearch'
    | 'GoogleSearch'
    | 'Ark';

// 通用密钥状态
export type CommonKeyStatus = 'ACTIVE' | 'DISABLED' | 'AUTH_FAILED';

// Claude账户类型
export type ClaudeType = 'PRO' | 'MAX_100' | 'MAX_200';

// Claude Code 刷新信息
export interface ClaudeCodeRefreshInfo {
    refreshToken: string;
    expiresAt?: number;
    clientId: string;
    email?: string;
    accountType?: ClaudeType;
    lastRefreshTime?: string;
}

// Google Vertex AI 刷新信息
export interface GoogleVertexAIRefreshInfo {
    projectId: string;
    clientEmail: string;
    privateKeyId: string;
    expiresAt?: number;
    lastRefreshTime?: string;
}

// 通用密钥响应
export interface CommonTokenKey {
    id: number;
    accessToken?: string;
    domain?: string;
    type: KeyChannel;
    status: CommonKeyStatus;
    name?: string;
    supportModels: string[];
    autoDisable?: boolean;
    modelMapping?: string;
    quota?: number;
    weight: number;
    windowSize: number;
    epsilon: number;
    errThreshold: number;
    minSampleSize: number;
    createdAt: string;
    updatedAt: string;
    claudeCodeRefreshInfo?: ClaudeCodeRefreshInfo;
    googleVertexAIRefreshInfo?: GoogleVertexAIRefreshInfo;
}

// 创建通用密钥请求
export interface CreateCommonTokenKeyRequest {
    accessToken?: string;
    domain?: string;
    type: KeyChannel;
    name?: string;
    supportModels: string[];
    autoDisable?: boolean;
    modelMapping?: string;
    quota?: number;
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
    minSampleSize?: number;
}

// 创建 Claude Code 密钥请求
export interface CreateClaudeCodeKeyRequest extends CreateCommonTokenKeyRequest {
    type: 'ClaudeCode';
    refreshToken: string;
    expiresAt?: number;
    clientId: string;
    email?: string;
    accountType?: ClaudeType;
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
}

// 更新通用密钥请求
export interface UpdateCommonTokenKeyRequest {
    id: number;
    accessToken?: string;
    domain?: string;
    status?: CommonKeyStatus;
    name?: string;
    supportModels?: string[];
    autoDisable?: boolean;
    modelMapping?: string;
    quota?: number;
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
}

// 更新 Claude Code 密钥请求
export interface UpdateClaudeCodeKeyRequest extends UpdateCommonTokenKeyRequest {
    refreshToken?: string;
    expiresAt?: number;
    clientId?: string;
    email?: string;
    accountType?: ClaudeType;
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
}

// 更新 Google Vertex AI 密钥请求
export interface UpdateGoogleVertexAIKeyRequest extends UpdateCommonTokenKeyRequest {
    // Google Vertex AI 特有字段通常不能直接更新
    // 因为它们来自服务账户JSON文件
    // 这里主要是为了保持接口一致性
    // 添加一个可选字段以避免空接口lint错误
    readonly _type?: 'GoogleVertexAI';
}

// Google Vertex AI 服务账户JSON格式
export interface GoogleServiceAccountJson {
    type: string;
    project_id: string;
    private_key_id: string;
    private_key: string;
    client_email: string;
    client_id: string;
    auth_uri: string;
    token_uri: string;
    auth_provider_x509_cert_url: string;
    client_x509_cert_url: string;
    universe_domain?: string;
}

// Google Vertex AI 导入请求
export interface ImportGoogleVertexAIKeyRequest {
    serviceAccountJson: string;
    name?: string;
    supportModels?: string[];
    autoDisable?: boolean;
    modelMapping?: string;
    quota?: number;
    weight?: number;
    windowSize?: number;
    epsilon?: number;
    errThreshold?: number;
}

// API 测试结果
export interface ApiTestResult {
    statusCode: number;
    body: string | null;
}

// 分页查询请求
export interface PageCommonTokenKeyRequest {
    pageNumber: number;
    pageSize: number;
    name?: string;
    domain?: string;
    status?: CommonKeyStatus;
    type?: KeyChannel;
}

// 批量创建请求
export interface BatchCreateCommonTokenKeyRequest {
    keys: CreateCommonTokenKeyRequest[];
}

// 批量创建Claude Code请求
export interface BatchCreateClaudeCodeKeyRequest {
    keys: CreateClaudeCodeKeyRequest[];
}

// 批量创建失败项
export interface BatchCreateFailure {
    index: number;
    data: CreateCommonTokenKeyRequest;
    error: string;
}

// 批量创建响应
export interface BatchCreateResponse {
    successful: CommonTokenKey[];
    failed: BatchCreateFailure[];
}

