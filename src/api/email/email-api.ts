import { apiClient } from "@/lib/apiClient";



export const emailApi = {
    sendEmail: async (email: string, type: 'register' | 'forgot-password') => {
        return await apiClient.post({
            url: '/api/email/send',
            params: {
                email,
                type
            }
        });
    },

    verifyCode: async (email: string, code: string) => {
        return await apiClient.post({
            url: '/api/email/verify-code',
            data: {
                email,
                code
            }
        });
    }
}