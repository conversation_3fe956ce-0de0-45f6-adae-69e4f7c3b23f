import { apiClient } from "@/lib/apiClient"
import type { UserWalletRecord } from "./user-wallet-model"


export const UserWalletApi = {


    getBalance: () => {
        return apiClient.get<number>({
            url: "/api/private/userWallet/balance"
        })
    },


    changeRecords: () => {
        return apiClient.get<UserWalletRecord[]>({
            url: "/api/private/userWallet/changeRecords"
        })
    }
}