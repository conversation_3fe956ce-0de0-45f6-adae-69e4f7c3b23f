import { apiClient } from "@/lib/apiClient";
import type { SystemModelResponse } from "./system-model-model";

export const systemModelApi = {
    /**
     * 获取所有模型
     */
    getAllModels: async (): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: "/api/private/systemModel/all"
        });
        return response;
    },

    /**
     * 获取显示的模型
     */
    getDisplayModels: async (): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: "/api/private/systemModel/display"
        });
        return response;
    },

    /**
     * 获取活跃的模型
     */
    getActiveModels: async (): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: "/api/private/systemModel/active"
        });
        return response;
    },

    /**
     * 根据厂商名称获取模型
     */
    getModelsByManufacturer: async (manufacturerName: string): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: `/api/private/systemModel/manufacturer/${manufacturerName}`
        });
        return response;
    },

    /**
     * 根据模型类型获取模型
     */
    getModelsByType: async (modelType: number): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: `/api/private/systemModel/type/${modelType}`
        });
        return response;
    },

    /**
     * 根据状态获取模型
     */
    getModelsByStatus: async (statuses: number): Promise<SystemModelResponse[]> => {
        const response = await apiClient.get<SystemModelResponse[]>({
            url: `/api/private/systemModel/status/${statuses}`
        });
        return response;
    },

    /**
     * 根据ID获取单个模型
     */
    getModelById: async (id: number): Promise<SystemModelResponse> => {
        const response = await apiClient.get<SystemModelResponse>({
            url: `/api/private/systemModel/${id}`
        });
        return response;
    }
};
