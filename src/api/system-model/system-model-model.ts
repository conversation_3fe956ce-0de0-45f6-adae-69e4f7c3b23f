/**
 * 系统模型响应类型
 */
export interface SystemModelResponse {
    /**
     * 主键ID
     */
    id: number;

    /**
     * 创建时间
     */
    createTime?: number;

    /**
     * 是否显示
     */
    isDisplay: boolean;

    /**
     * 是否探测
     */
    isProbe?: boolean;

    /**
     * Logo URL
     */
    logoUrl?: string;

    /**
     * 厂商名称
     */
    manufacturerName?: string;

    /**
     * 模型名称
     */
    modelName?: string;

    /**
     * 模型类型
     */
    modelType?: number;

    /**
     * 展示模型名称
     */
    presentationModel?: string;

    /**
     * 状态
     */
    statuses?: number;

    /**
     * 消失时间
     */
    extinctionTime?: number;
}
