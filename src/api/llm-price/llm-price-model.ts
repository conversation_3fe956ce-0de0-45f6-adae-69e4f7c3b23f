/**
 * LLM价格相关的类型定义
 */

/**
 * LLM价格查询请求
 */
export interface LlmPriceQueryRequest {
  page?: number;
  size?: number;
  systemModelId?: number;
  statuses?: number;
}

/**
 * LLM价格创建请求
 */
export interface LlmPriceCreateRequest {
  systemModelId: string;
  statuses?: number;
  audioCachePrompt?: string;
  audioCompletion?: string;
  audioPrompt?: string;
  imageCachePrompt?: string;
  imageCompletion?: string;
  imagePrompt?: string;
  reasoningCompletion?: string;
  textCachePrompt?: string;
  textCachePromptWrite1h?: string;
  textCachePromptWrite5m?: string;
  textCompletion?: string;
  textPrompt?: string;
}

/**
 * LLM价格更新请求
 */
export interface LlmPriceUpdateRequest {
  statuses?: number;
  audioCachePrompt?: string;
  audioCompletion?: string;
  audioPrompt?: string;
  imageCachePrompt?: string;
  imageCompletion?: string;
  imagePrompt?: string;
  reasoningCompletion?: string;
  textCachePrompt?: string;
  textCachePromptWrite1h?: string;
  textCachePromptWrite5m?: string;
  textCompletion?: string;
  textPrompt?: string;
}

/**
 * LLM价格响应
 */
export interface LlmPriceResponse {
  id: number;
  systemModelId: number;
  systemModelName?: string;
  manufacturerName?: string;
  statuses: number;
  createTime?: number;
  audioCachePrompt?: string;
  audioCompletion?: string;
  audioPrompt?: string;
  imageCachePrompt?: string;
  imageCompletion?: string;
  imagePrompt?: string;
  reasoningCompletion?: string;
  textCachePrompt?: string;
  textCachePromptWrite1h?: string;
  textCachePromptWrite5m?: string;
  textCompletion?: string;
  textPrompt?: string;
}

/**
 * 价格状态
 */
export type PriceStatus = 0 | 1 | 2;

export const PriceStatus = {
  INACTIVE: 0 as const,
  ACTIVE: 1 as const,
  DEPRECATED: 2 as const,
} as const;

/**
 * 价格状态选项
 */
export const PRICE_STATUS_OPTIONS = [
  { label: "未激活", value: PriceStatus.INACTIVE },
  { label: "已激活", value: PriceStatus.ACTIVE },
  { label: "已弃用", value: PriceStatus.DEPRECATED },
];