import { apiClient } from "@/lib/apiClient";
import type {
  LlmPriceQueryRequest,
  LlmPriceCreateRequest,
  LlmPriceUpdateRequest,
  LlmPriceResponse,
} from "./llm-price-model";
import type { FetchResponse } from "@/components/table/types";

export const llmPriceApi = {
  /**
   * 分页查询LLM价格列表
   */
  queryPage: async (params: LlmPriceQueryRequest): Promise<FetchResponse<LlmPriceResponse>> => {
    const response = await apiClient.post<FetchResponse<LlmPriceResponse>>({
      url: "/api/private/llm-prices/page",
      data: params,
    });
    return response;
  },

  /**
   * 创建LLM价格
   */
  create: async (data: LlmPriceCreateRequest): Promise<LlmPriceResponse> => {
    const response = await apiClient.post<LlmPriceResponse>({
      url: "/api/private/llm-prices",
      data,
    });
    return response;
  },

  /**
   * 获取LLM价格详情
   */
  getById: async (id: number): Promise<LlmPriceResponse> => {
    const response = await apiClient.get<LlmPriceResponse>({
      url: `/api/private/llm-prices/${id}`,
    });
    return response;
  },

  /**
   * 更新LLM价格
   */
  update: async (id: number, data: LlmPriceUpdateRequest): Promise<LlmPriceResponse> => {
    const response = await apiClient.put<LlmPriceResponse>({
      url: `/api/private/llm-prices/${id}`,
      data,
    });
    return response;
  },

  /**
   * 删除LLM价格
   */
  delete: async (id: number): Promise<void> => {
    await apiClient.delete({
      url: `/api/private/llm-prices/${id}`,
    });
  },

  /**
   * 根据系统模型ID查询价格列表
   */
  getBySystemModelId: async (systemModelId: number): Promise<LlmPriceResponse[]> => {
    const response = await apiClient.get<LlmPriceResponse[]>({
      url: `/api/private/llm-prices/by-model/${systemModelId}`,
    });
    return response;
  },
};