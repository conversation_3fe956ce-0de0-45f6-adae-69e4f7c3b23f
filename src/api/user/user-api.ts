import { userToken<PERSON><PERSON> } from "@/lib/apiClient";
import { apiClient } from "@/lib/apiClient";
import type { UserProfile } from "./user-model";
import type { UserWithToken } from "./user-model";


export const userApi = {


    login: async (data: { email: string; password: string }): Promise<UserWithToken> => {
        return await apiClient.post({
            url: "/api/user/auth/password/login",
            data: {
                email: data.email,
                password: data.password,
            },
        });
    },

    logout: async (): Promise<void> => {
        localStorage.removeItem(userTokenKey);
        window.location.href = import.meta.env.VITE_LOGIN_URL;
    },


    profile: async (): Promise<UserProfile> => {
        return await apiClient.get({
            url: "/api/user/auth/profile",
        });
    },


    setupPassword: async (email: string, password: string, code: string) => {
        return await apiClient.post<{ accessToken: string }>({
            url: `/api/user/auth/setup-password`,
            data: { email, password, code }
        });
    },

    forgetPassword: async (email: string, password: string, code: string) => {
        return await apiClient.post<{ accessToken: string }>({
            url: `/api/user/auth/forgot-password`,
            data: { email, password, code }
        });
    },


};
