export interface OrganizationWalletRecord {
    id: string;
    organizationId: string;
    organizationWalletId: string;
    amountOfMoney: string;
    /**
     *  0 赠送
     *  1 充值
     *  2 退款
     */
    amountOfMoneyType: number;
    createTime: string;
    statuses: number;
}

// 更新自动钱包参数接口
export interface UpdateAutoWalletParams {
    organizationId: string;
    minimumAmount: number;
    rechargeAmount: number;
}


export interface WalletResponse {
    balance: number;
    minimumAmount: number;
    rechargeAmount: number;
}