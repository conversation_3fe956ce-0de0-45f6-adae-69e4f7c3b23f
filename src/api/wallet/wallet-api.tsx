import { apiClient } from "@/lib/apiClient"
import type { OrganizationWalletRecord, UpdateAutoWalletParams, WalletResponse } from "./wallet-model"

export const WalletApi = {

    /**
     * 查询变动记录
     * @param organizationId 组织ID
     * @returns 变动记录列表
     */
    getOrganizationWalletRecord: (organizationId: string) => {
        return apiClient.get<OrganizationWalletRecord[]>({
            url: "/api/private/organizationWallet/changeRecords",
            params: {
                organizationId
            }
        })
    },

    /**
     * 设置最低金额充值
     * @param params 更新自动钱包参数
     * @returns 操作结果
     */
    updateAutoWallet: (params: UpdateAutoWalletParams) => {
        return apiClient.put<string>({
            url: "/api/private/organizationWallet/updateAutoWallet",
            params: {
                organizationId: params.organizationId,
                minimumAmount: params.minimumAmount,
                rechargeAmount: params.rechargeAmount
            }
        })
    },

    /**
     * 查询余额
     * @param organizationId 组织ID
     * @returns 余额
     */
    getBalance: (organizationId: string): Promise<WalletResponse> => {
        return apiClient.get<WalletResponse>({
            url: "/api/private/organizationWallet/getWalletBalance",
            params: {
                organizationId
            }
        })
    },

    addPayment: (organizationId: string, amount: number) => {
        return apiClient.post({
            url: "/api/private/stripePayment/payment",
            params: {
                organizationId,
                amount
            }
        })
    },

    /**
     * 导出账单
     * @param organizationId 组织ID
     * @param start 开始时间 yyyy-MM-dd
     * @param end 结束时间 yyyy-MM-dd
     * @returns 账单
     */
    exportBill: (organizationId: string, start: string, end: string) => {
        return apiClient.post<string>({
            url: "/api/private/organizationDailyBill/download",
            params: {
                organizationId,
                start,
                end
            },
            responseType: 'blob',
        })
    }
}