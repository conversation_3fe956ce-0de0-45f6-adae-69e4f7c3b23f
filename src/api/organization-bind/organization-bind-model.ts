import { apiClient } from "@/lib/apiClient"
import type { OrganizationBind } from "./organization-bind-api"



export const OrganizationBindApi = {

    persistOrganizationBind: async (organizationId: string, permission: number, userEmail: string) => {
        return apiClient.post({
            url: "/api/private/userOrganizationBind/persistenceUserOrganizationBind",
            params: {
                organizationId,
                permission,
                userEmail
            }
        })
    },

    getOrganizationBind: async (organizationId: string): Promise<OrganizationBind[]> => {
        return apiClient.get({
            url: "/api/private/userOrganizationBind/selectUserOrganizationBindByOrganizationId",
            params: {
                organizationId,
            }
        })
    },

    updateOrganizationBind: async (organizationId: string, userEmail: string, permission: number) => {
        return apiClient.put({
            url: "/api/private/userOrganizationBind/updateUserOrganizationBindByOrganizationId",
            params: {
                organizationId,
                userEmail,
                permission
            }
        })
    },
    deleteOrganizationBind: async (organizationId: string, userEmail: string) => {
        return apiClient.delete({
            url: "/api/private/userOrganizationBind/deleteUserOrganizationBindByOrganizationId",
            params: {
                organizationId,
                userEmail
            }
        })
    }
}