import { apiClient } from "@/lib/apiClient";
import type {
    KeyMonitoringOverview,
    ModelKeyStats,
    KeyPerformanceTrend,
    SchedulerConfigRequest,
    KeyUsageResultRequest
} from "./key-monitoring-types";

export const keyMonitoringApi = {
    /**
     * 获取密钥监控概览数据
     */
    getOverview: async () => {
        return await apiClient.get<KeyMonitoringOverview>({
            url: '/api/v1/key-stats/overview'
        });
    },

    /**
     * 获取所有模型的密钥统计信息
     */
    getAllModelsStats: async () => {
        return await apiClient.get<Record<string, ModelKeyStats>>({
            url: '/api/v1/key-stats/models/stats'
        });
    },

    /**
     * 获取指定模型的密钥统计信息
     */
    getModelStats: async (modelName: string) => {
        return await apiClient.get<Record<string, any>>({
            url: `/api/v1/key-stats/model/${modelName}`
        });
    },

    /**
     * 获取密钥性能趋势数据
     */
    getPerformanceTrends: async (hours: number = 24) => {
        return await apiClient.get<KeyPerformanceTrend[]>({
            url: '/api/v1/key-stats/performance/trends',
            params: { hours }
        });
    },

    /**
     * 更新密钥权重
     */
    updateKeyWeight: async (keyId: number, weight: number) => {
        return await apiClient.post<boolean>({
            url: `/api/v1/key-stats/key/${keyId}/weight/${weight}`,
            data: {}
        });
    },

    /**
     * 调整模型的调度器配置
     */
    adjustSchedulerConfig: async (modelName: string, config: SchedulerConfigRequest) => {
        return await apiClient.post<boolean>({
            url: `/api/v1/key-stats/model/${modelName}/config`,
            data: config
        });
    },

    /**
     * 记录密钥使用结果
     */
    recordKeyUsageResult: async (modelName: string, keyId: number, request: KeyUsageResultRequest) => {
        return await apiClient.post<boolean>({
            url: `/api/v1/key-stats/model/${modelName}/key/${keyId}/result`,
            data: request
        });
    },

    /**
     * 获取最优密钥
     */
    getBestKey: async (modelName: string, channels?: string[]) => {
        const params = channels ? { channels: channels.join(',') } : {};
        return await apiClient.get<any>({
            url: `/api/v1/key-stats/best-key/${modelName}`,
            params
        });
    }
};
