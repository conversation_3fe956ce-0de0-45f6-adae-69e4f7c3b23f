// 密钥监控概览数据
export interface KeyMonitoringOverview {
    totalKeys: number;
    activeKeys: number;
    disabledKeys: number;
    authFailedKeys: number;
    totalModels: number;
    channelDistribution: Record<string, number>;
    avgSuccessRate: number;
    totalRequests: number;
    lastUpdateTime: string;
}

// 模型密钥统计信息
export interface ModelKeyStats {
    modelName: string;
    keyCount: number;
    avgSuccessRate: number;
    totalRequests: number;
    bestKeyId?: number;
    worstKeyId?: number;
    schedulerConfig: SchedulerConfigInfo;
    keyStats: Record<string, KeyStatInfo>;
}

// 调度器配置信息
export interface SchedulerConfigInfo {
    epsilon: number;
    errThreshold: number;
    windowSize: number;
    minSampleSize: number;
}

// 密钥统计信息
export interface KeyStatInfo {
    keyId: string;
    weight: number;
    errorRate: number;
    score: number;
    requestCount: number;
    successCount: number;
    successRate: number;
    channel: string;
    name?: string;
}

// 密钥性能趋势数据
export interface KeyPerformanceTrend {
    timestamp: string;
    keyId: number;
    modelName: string;
    successRate: number;
    requestCount: number;
    avgResponseTime?: number;
    errorRate: number;
    channel: string;
}

// 调度器配置请求
export interface SchedulerConfigRequest {
    epsilon?: number;
    errThreshold?: number;
    windowSize?: number;
    minSampleSize?: number;
}

// 密钥使用结果请求
export interface KeyUsageResultRequest {
    success: boolean;
    needDeleted?: boolean;
}
