import axios, { AxiosError, type AxiosRequestConfig, type AxiosResponse } from 'axios';


export const userTokenKey = "hispreadUserToken"

// 创建 axios 实例
const axiosInstance = axios.create({
    timeout: 500000,
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
});

export interface PlatformResult<T = any> {
    requestId: number;
    code: string;
    desc: string;
    data: T;
    count?: number;
    success: boolean;
}

axiosInstance.interceptors.request.use(
    (config) => {
        let token = localStorage.getItem(userTokenKey) ?? '';
        config.headers.Authorization = `Bearer ${removeQuotesBoth(token)} `;

        // 添加用户当前时区到请求头
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        config.headers['x-server-timezone'] = userTimezone;

        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);


// 响应拦截
axiosInstance.interceptors.response.use((res: any) => {
    if (res.data instanceof Blob) {
        return res;
    }
    // 对于其他类型的响应，返回数据部分
    const result = res.data
    if (result.success) {
        if (result.count) {
            return {
                content: result.data.content,
                total: result.data.total
            };
        }
        return result.data;
    } else {
        return Promise.reject(new Error(result.desc));
    }
},
    (error: AxiosError<any>) => {
        if (error.status === 401 || error.status === 403) {
            localStorage.removeItem(userTokenKey);
            window.location.href = import.meta.env.VITE_LOGIN_URL;
            return Promise.reject(new Error('Login verification failed'));
        }
        const { response, message } = error || {};
        let errMsg = '';
        try {
            errMsg = response?.data?.message || response?.data?.desc || message;
        } catch (error) {
            throw new Error(error as unknown as string);
        }
        if (errMsg === '') {
            errMsg = "system error";
        }
        // console.error(errMsg);
        return Promise.reject(new Error(errMsg));
    },
);

class APIClient {
    get<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'GET' });
    }

    post<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'POST' });
    }

    put<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'PUT' });
    }

    delete<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'DELETE' });
    }

    patch<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return this.request({ ...config, method: 'PATCH' });
    }

    request<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (typeof window === 'undefined') return Promise.resolve({} as T);
        return new Promise((resolve, reject) => {
            axiosInstance
                .request<any, AxiosResponse<any>>(config)
                .then((res: AxiosResponse<any>) => {
                    resolve(res as unknown as Promise<T>);
                })
                .catch((e: Error | AxiosError) => {
                    reject(e);
                });
        });
    }
}



function removeQuotesBoth(str: string): string {
    return str.replace(/^['"]|['"]$/g, '');
}


const apiClient = new APIClient();
export { apiClient };


