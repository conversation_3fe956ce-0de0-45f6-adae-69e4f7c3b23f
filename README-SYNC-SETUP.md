# Google Vertex AI 同步功能设置指南

## 当前状态

目前MySQL同步功能已经**暂时禁用**，以确保应用能够正常启动。主要的PostgreSQL功能保持正常工作。

## 已完成的工作

### 1. 数据库配置
- ✅ 添加了MySQL驱动依赖
- ✅ 配置了MySQL数据源（暂时禁用）
- ✅ 创建了Channel实体映射
- ✅ 创建了ChannelSyncStatus实体（使用PostgreSQL）

### 2. 同步框架
- ✅ 创建了ChannelSyncService（暂时禁用MySQL功能）
- ✅ 创建了ChannelSyncTimer（暂时禁用定时任务）
- ✅ 创建了测试API接口

### 3. API接口
- ✅ `GET /api/v1/sync/test-connections` - 测试数据源连接
- ✅ `POST /api/v1/sync/google-vertex-ai/trigger` - 手动触发同步（暂时禁用）
- ✅ `GET /api/v1/sync/google-vertex-ai/status` - 获取同步状态

## 启用MySQL同步功能的步骤

当您准备启用MySQL同步功能时，请按以下步骤操作：

### 1. 启用MySQL数据源配置
编辑 `src/main/kotlin/io/cliveyou/claudecodeproxybackend/config/DataSourceConfig.kt`：
```kotlin
// 取消注释以下行：
@Configuration
@EnableJpaRepositories(
    basePackages = ["io.cliveyou.claudecodeproxybackend.sync.infrastructure.repository"],
    entityManagerFactoryRef = "mysqlEntityManagerFactory",
    transactionManagerRef = "mysqlTransactionManager"
)
```

### 2. 启用Bean定义
在同一文件中取消注释所有的@Bean方法。

### 3. 启用ChannelSyncService功能
编辑 `src/main/kotlin/io/cliveyou/claudecodeproxybackend/sync/domain/ChannelSyncService.kt`：
- 取消注释channelRepository参数
- 恢复完整的同步逻辑

### 4. 启用定时任务
编辑 `src/main/kotlin/io/cliveyou/claudecodeproxybackend/sync/application/timer/ChannelSyncTimer.kt`：
```kotlin
// 取消注释：
@Scheduled(fixedRate = 1800000) // 30分钟
```

### 5. 启用测试服务
编辑 `src/main/kotlin/io/cliveyou/claudecodeproxybackend/sync/application/service/ChannelSyncTestService.kt`：
- 取消注释channelRepository参数
- 恢复MySQL连接测试逻辑

## 测试步骤

### 1. 启动应用
```bash
./gradlew bootRun
```

### 2. 测试PostgreSQL连接
```bash
curl -X GET http://localhost:8070/api/v1/sync/test-connections
```

应该返回：
```json
{
  "code": "200",
  "desc": "操作成功",
  "data": {
    "mysql_connection": "DISABLED",
    "mysql_message": "MySQL connection temporarily disabled",
    "postgresql_connection": "SUCCESS",
    "postgresql_sync_status_count": 0
  },
  "success": true
}
```

### 3. 测试同步API
```bash
curl -X POST http://localhost:8070/api/v1/sync/google-vertex-ai/trigger
```

应该返回：
```json
{
  "code": "200",
  "desc": "操作成功",
  "data": {
    "success": true,
    "syncCount": 0,
    "message": "Sync functionality temporarily disabled",
    "duration": 5
  },
  "success": true
}
```

## 故障排除

如果应用仍然无法启动，请检查：

1. **PostgreSQL连接**：确保PostgreSQL数据库可访问
2. **依赖冲突**：检查是否有其他JPA配置冲突
3. **端口占用**：确保8070端口未被占用

## 下一步

一旦应用能够正常启动，您就可以按照上述步骤逐步启用MySQL同步功能。建议先在测试环境中验证MySQL连接，然后再启用完整的同步逻辑。
